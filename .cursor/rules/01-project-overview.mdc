---
description:
globs:
alwaysApply: true
---
# WMS 仓库管理系统 - 项目概述

本项目是基于 [JeecgBoot](mdc:README.md) 低代码开发平台构建的仓库管理系统（Warehouse Management System），使用Java Spring Boot框架开发。

## 项目结构

项目基于JeecgBoot的多模块架构，主要包含以下模块：

- [wms-admin](mdc:wms-admin/pom.xml) - WMS系统核心模块
- [jeecg-boot-base-core](mdc:jeecg-boot-base-core/pom.xml) - 共通模块（工具类、配置、权限等）
- [jeecg-module-system](mdc:jeecg-module-system/pom.xml) - 系统管理模块

## 核心功能

仓库管理系统提供以下主要功能：

1. 仓库管理 - 仓库信息维护，支持多种仓库类型（平库、立库、线边库等）
2. 库位管理 - 库位状态跟踪（无货、有货、入库中、出库中、异常冻结）
3. 库存管理 - 实时库存查询、库存盘点
4. 出入库管理 - 支持多种业务场景（生产入库、销售出库、采购入库等）

## 技术架构

- 前端：Vue3 + Ant Design Vue
- 后端：Spring Boot + MyBatis-Plus
- 数据库：MySQL/Oracle
- 微服务：Spring Cloud Alibaba