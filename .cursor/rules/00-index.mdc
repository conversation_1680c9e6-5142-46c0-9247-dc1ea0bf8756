---
description:
globs:
alwaysApply: true
---
# WMS系统 Cursor 规则索引

以下是WMS仓库管理系统的Cursor规则，这些规则帮助你理解项目结构和业务逻辑：

## 规则列表

1. [项目概述](mdc:.cursor/rules/01-project-overview.mdc) - 系统基本介绍、项目结构与核心功能
2. [核心领域模型](mdc:.cursor/rules/02-core-domain-model.mdc) - 系统核心实体与关系
3. [技术架构与代码规范](mdc:.cursor/rules/03-tech-architecture.mdc) - 技术栈与编码规范
4. [核心业务流程](mdc:.cursor/rules/04-business-processes.mdc) - 系统主要业务流程说明

## 快速入口

### 核心实体
- [仓库实体(WmsWarehouse)](mdc:wms-admin/src/main/java/org/jeecg/modules/admin/entity/WmsWarehouse.java)
- [仓库服务实现](mdc:wms-admin/src/main/java/org/jeecg/modules/admin/service/impl/WmsWarehouseServiceImpl.java)
- [库位服务实现](mdc:wms-admin/src/main/java/org/jeecg/modules/admin/service/impl/WmsLocateServiceImpl.java)
- [库存盘点计划服务](mdc:wms-admin/src/main/java/org/jeecg/modules/admin/service/impl/WmsInventoryCountPlanServiceImpl.java)

### 业务常量
- [WMS业务常量](mdc:wms-admin/src/main/java/org/jeecg/modules/admin/constant/WmsConstant.java)

### 项目配置
- [主POM文件](mdc:pom.xml)
- [WMS模块POM](mdc:wms-admin/pom.xml)