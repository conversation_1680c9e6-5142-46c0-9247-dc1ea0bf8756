<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.admin.mapper.WmsStockdetailMapper">

    <select id="listSummary" resultType="org.jeecg.modules.admin.entity.WmsStockdetail">
        SELECT *
        FROM (SELECT materiel_type,
        item_code,
        item_name,
        item_spec,
        item_unit,
        batch_code,
        is_forbid,
        inv_state,
        erp_posting,
        zone_code,
        MAX(create_time) AS create_time,  <!-- 使用 MAX 聚合函数处理 create_time -->
        SUM(quantity) AS quantity
        FROM wms_stockdetail
        GROUP BY materiel_type,
        item_code,
        item_name,
        item_spec,
        item_unit,
        batch_code,
        is_forbid,
        inv_state,
        zone_code,
        erp_posting) n
        ${ew.customSqlSegment};
    </select>
    <select id="queryWithNullObDtlId" resultType="org.jeecg.modules.admin.entity.WmsStockdetail">
        SELECT *
        FROM (
        SELECT
        a.*
        FROM
        wms_stockdetail a
        LEFT JOIN wms_locate b ON a.locate_code = b.locate_code
        WHERE
        1 = 1
        AND b.lk_locate = '1'
        AND a.erp_lock = '0'
        AND a.is_forbid = '0'
        AND b.locate_state = '1'
        AND IFNULL( a.ob_dtl_id, '' ) = ''
        AND IFNULL( a.target_position, '' ) = ''
        AND b.locate_operate_state IN ( '0', '1' )
        AND a.inv_state = '1'
        <if test="levelNo != null and levelNo != ''">
            AND b.level_no = #{levelNo}
        </if>
        ORDER BY
        b.col_no,b.distribution_depth ASC) n
        ${ew.customSqlSegment};
    </select>
    <select id="queryWithNullObDtlIdForFlat" resultType="org.jeecg.modules.admin.entity.WmsStockdetail">
        SELECT *
        FROM (
        SELECT
            a.*
        FROM
            wms_stockdetail a
        WHERE
            1 = 1
          AND a.materiel_type = 'YL'
          AND a.erp_lock = '0'
          AND a.is_forbid = '0'
          AND IFNULL( a.ob_dtl_id, '' ) = ''
          AND IFNULL( a.target_position, '' ) = ''
          AND a.inv_state = '1') n
            ${ew.customSqlSegment};
    </select>

    <select id="getCheckStock" resultType="org.jeecg.modules.admin.entity.WmsStockdetail">
        SELECT
            a.*
        FROM
            wms_stockdetail a
            LEFT JOIN wms_locate b ON a.locate_code = b.locate_code
        WHERE
            a.erp_lock = '0'
        AND a.is_forbid = '0'
        AND b.locate_state = '1'
        AND IFNULL( a.ob_dtl_id, '' ) = ''
        AND IFNULL( a.target_position, '' ) = ''
        AND b.locate_operate_state IN ( '0', '1' )
        AND a.inv_state = '1'
        <if test="wmsInventoryCountPlan.lkLocate != null and wmsInventoryCountPlan.lkLocate != ''">
            AND b.lk_locate = #{wmsInventoryCountPlan.lkLocate}
        </if>
        <if test="wmsInventoryCountPlan.warehouseCode != null and wmsInventoryCountPlan.warehouseCode != ''">
            and a.warehouse_code=#{wmsInventoryCountPlan.warehouseCode}
        </if>
        <if test="wmsInventoryCountPlan.fromMaterielCode != '' or wmsInventoryCountPlan.toMaterielCode != ''">
            <choose>
                <when test="wmsInventoryCountPlan.fromMaterielCode != '' and wmsInventoryCountPlan.toMaterielCode != ''">
                    and a.item_code &gt;= #{wmsInventoryCountPlan.fromMaterielCode} and a.item_code &lt;= #{wmsInventoryCountPlan.toMaterielCode}
                </when>
                <when test="wmsInventoryCountPlan.fromMaterielCode != '' and wmsInventoryCountPlan.toMaterielCode == ''">
                    and a.item_code = #{wmsInventoryCountPlan.fromMaterielCode}
                </when>
                <when test="wmsInventoryCountPlan.fromMaterielCode == '' and wmsInventoryCountPlan.toMaterielCode != ''">
                    and a.item_code = #{wmsInventoryCountPlan.toMaterielCode}
                </when>
            </choose>
        </if>

        <if test="wmsInventoryCountPlan.fromBatchCode != '' or wmsInventoryCountPlan.toBatchCode != ''">
            <choose>
                <when test="wmsInventoryCountPlan.fromBatchCode != '' and wmsInventoryCountPlan.toBatchCode != ''">
                    and a.batch_code &gt;= #{wmsInventoryCountPlan.fromBatchCode} and a.batch_code &lt;= #{wmsInventoryCountPlan.toBatchCode}
                </when>
                <when test="wmsInventoryCountPlan.fromBatchCode != '' and wmsInventoryCountPlan.toBatchCode == ''">
                    and a.batch_code = #{wmsInventoryCountPlan.fromBatchCode}
                </when>
                <when test="wmsInventoryCountPlan.fromBatchNo == '' and wmsInventoryCountPlan.toBatchCode != ''">
                    and a.batch_code = #{wmsInventoryCountPlan.toBatchCode}
                </when>
            </choose>
        </if>

        <if test="wmsInventoryCountPlan.fromLpn != '' or wmsInventoryCountPlan.toLpn != ''">
            <choose>
                <when test="wmsInventoryCountPlan.fromLpn != '' and wmsInventoryCountPlan.toLpn != ''">
                    and a.lpn &gt;= #{wmsInventoryCountPlan.fromLpn} and a.lpn &lt;= #{wmsInventoryCountPlan.toLpn}
                </when>
                <when test="wmsInventoryCountPlan.fromLpn != '' and wmsInventoryCountPlan.toLpn == ''">
                    and a.lpn = #{wmsInventoryCountPlan.fromLpn}
                </when>
                <when test="wmsInventoryCountPlan.fromTrayCode == '' and wmsInventoryCountPlan.toLpn != ''">
                    and a.lpn = #{wmsInventoryCountPlan.toLpn}
                </when>
            </choose>
        </if>

        <if test="wmsInventoryCountPlan.locateCode != null and wmsInventoryCountPlan.locateCode != ''">
            and a.locate_code = #{wmsInventoryCountPlan.locateCode}
        </if>
    </select>
    
    <!-- 新增查询方法：按照品号、品名、批号、区域相同的条件汇总库存 -->
    <select id="queryMaterialSummary" resultType="org.jeecg.modules.admin.entity.WmsStockdetail">
        SELECT
            item_code,
            item_name,
            batch_code,
            zone_code,
            MAX(item_barcode) AS item_barcode,
            MAX(locate_code) AS locate_code,
            MAX(create_time) AS create_time,
            MAX(materiel_type) AS materiel_type,
            MAX(product_date) AS product_date,
            SUM(quantity) AS quantity
        FROM
            wms_stockdetail
        ${ew.customSqlSegment}
        GROUP BY
            item_code,
            item_name,
            batch_code,
            zone_code
    </select>

    <!-- 添加queryStockAlert方法的SQL实现 -->
    <select id="queryStockAlert" resultType="java.util.Map">
        SELECT 
            s.item_code,
            s.item_name,
            s.item_spec,
            s.materiel_type,
            SUM(s.quantity) AS total_quantity,
            m.min_value,
            m.max_value,
            MAX(DATEDIFF(CURRENT_DATE, s.create_time)) AS max_stock_age,
            CASE 
                WHEN SUM(s.quantity) &gt; m.max_value THEN '库存积压'
                WHEN SUM(s.quantity) &lt; m.min_value THEN '库存不足'
                ELSE '库存正常'
            END AS 库存状态
        FROM 
            wms_stockdetail s
        LEFT JOIN 
            wms_spec_match_item m ON s.item_code COLLATE utf8mb4_unicode_ci = m.item_code COLLATE utf8mb4_unicode_ci
        GROUP BY 
            s.item_code, s.item_name, s.item_spec, s.materiel_type, m.min_value, m.max_value
        ORDER BY 
            CASE 
                WHEN SUM(s.quantity) &gt; m.max_value THEN 1
                WHEN SUM(s.quantity) &lt; m.min_value THEN 1
                ELSE 2
            END,
            max_stock_age DESC,
            s.item_code
    </select>
</mapper>