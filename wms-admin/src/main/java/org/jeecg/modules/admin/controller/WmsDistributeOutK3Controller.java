package org.jeecg.modules.admin.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.entity.WmsDistributeOutK3;
import org.jeecg.modules.admin.service.IWmsDistributeOutK3Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 分布式调出单
 * @Author: jeecg-boot
 * @Date:   2024-10-26
 * @Version: V1.0
 */
@Api(tags="分布式调出单")
@RestController
@RequestMapping("/admin/wmsDistributeOutK3")
@Slf4j
public class WmsDistributeOutK3Controller extends JeecgController<WmsDistributeOutK3, IWmsDistributeOutK3Service> {
	@Autowired
	private IWmsDistributeOutK3Service wmsDistributeOutK3Service;
	
	/**
	 * 分页列表查询
	 *
	 * @param wmsDistributeOutK3
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "分布式调出单-分页列表查询")
	@ApiOperation(value="分布式调出单-分页列表查询", notes="分布式调出单-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WmsDistributeOutK3>> queryPageList(WmsDistributeOutK3 wmsDistributeOutK3,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WmsDistributeOutK3> queryWrapper = QueryGenerator.initQueryWrapper(wmsDistributeOutK3, req.getParameterMap());
		Page<WmsDistributeOutK3> page = new Page<WmsDistributeOutK3>(pageNo, pageSize);
		IPage<WmsDistributeOutK3> pageList = wmsDistributeOutK3Service.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param wmsDistributeOutK3
	 * @return
	 */
	@AutoLog(value = "分布式调出单-添加")
	@ApiOperation(value="分布式调出单-添加", notes="分布式调出单-添加")
	@RequiresPermissions("admin:wms_distribute_out_k3:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WmsDistributeOutK3 wmsDistributeOutK3) {
		wmsDistributeOutK3Service.save(wmsDistributeOutK3);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param wmsDistributeOutK3
	 * @return
	 */
	@AutoLog(value = "分布式调出单-编辑")
	@ApiOperation(value="分布式调出单-编辑", notes="分布式调出单-编辑")
	@RequiresPermissions("admin:wms_distribute_out_k3:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WmsDistributeOutK3 wmsDistributeOutK3) {
		wmsDistributeOutK3Service.updateById(wmsDistributeOutK3);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "分布式调出单-通过id删除")
	@ApiOperation(value="分布式调出单-通过id删除", notes="分布式调出单-通过id删除")
	@RequiresPermissions("admin:wms_distribute_out_k3:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		wmsDistributeOutK3Service.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "分布式调出单-批量删除")
	@ApiOperation(value="分布式调出单-批量删除", notes="分布式调出单-批量删除")
	@RequiresPermissions("admin:wms_distribute_out_k3:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.wmsDistributeOutK3Service.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "分布式调出单-通过id查询")
	@ApiOperation(value="分布式调出单-通过id查询", notes="分布式调出单-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WmsDistributeOutK3> queryById(@RequestParam(name="id",required=true) String id) {
		WmsDistributeOutK3 wmsDistributeOutK3 = wmsDistributeOutK3Service.getById(id);
		if(wmsDistributeOutK3==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(wmsDistributeOutK3);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param wmsDistributeOutK3
    */
    @RequiresPermissions("admin:wms_distribute_out_k3:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WmsDistributeOutK3 wmsDistributeOutK3) {
        return super.exportXls(request, wmsDistributeOutK3, WmsDistributeOutK3.class, "分布式调出单");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:wms_distribute_out_k3:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WmsDistributeOutK3.class);
    }

}
