package org.jeecg.modules.admin.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.entity.WmsStockDump;
import org.jeecg.modules.admin.service.IWmsStockDumpService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 库存转储单
 * @Author: jeecg-boot
 * @Date:   2024-12-16
 * @Version: V1.0
 */
@Api(tags="库存转储单")
@RestController
@RequestMapping("/admin/wmsStockDump")
@Slf4j
public class WmsStockDumpController extends JeecgController<WmsStockDump, IWmsStockDumpService> {
	@Autowired
	private IWmsStockDumpService wmsStockDumpService;
	
	/**
	 * 分页列表查询
	 *
	 * @param wmsStockDump
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "库存转储单-分页列表查询")
	@ApiOperation(value="库存转储单-分页列表查询", notes="库存转储单-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WmsStockDump>> queryPageList(WmsStockDump wmsStockDump,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WmsStockDump> queryWrapper = QueryGenerator.initQueryWrapper(wmsStockDump, req.getParameterMap());
		Page<WmsStockDump> page = new Page<WmsStockDump>(pageNo, pageSize);
		IPage<WmsStockDump> pageList = wmsStockDumpService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param wmsStockDump
	 * @return
	 */
	@AutoLog(value = "库存转储单-添加")
	@ApiOperation(value="库存转储单-添加", notes="库存转储单-添加")
	@RequiresPermissions("admin:wms_stock_dump:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WmsStockDump wmsStockDump) {
		wmsStockDumpService.save(wmsStockDump);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param wmsStockDump
	 * @return
	 */
	@AutoLog(value = "库存转储单-编辑")
	@ApiOperation(value="库存转储单-编辑", notes="库存转储单-编辑")
	@RequiresPermissions("admin:wms_stock_dump:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WmsStockDump wmsStockDump) {
		wmsStockDumpService.updateById(wmsStockDump);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "库存转储单-通过id删除")
	@ApiOperation(value="库存转储单-通过id删除", notes="库存转储单-通过id删除")
	@RequiresPermissions("admin:wms_stock_dump:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		wmsStockDumpService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "库存转储单-批量删除")
	@ApiOperation(value="库存转储单-批量删除", notes="库存转储单-批量删除")
	@RequiresPermissions("admin:wms_stock_dump:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.wmsStockDumpService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "库存转储单-通过id查询")
	@ApiOperation(value="库存转储单-通过id查询", notes="库存转储单-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WmsStockDump> queryById(@RequestParam(name="id",required=true) String id) {
		WmsStockDump wmsStockDump = wmsStockDumpService.getById(id);
		if(wmsStockDump==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(wmsStockDump);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param wmsStockDump
    */
    @RequiresPermissions("admin:wms_stock_dump:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WmsStockDump wmsStockDump) {
        return super.exportXls(request, wmsStockDump, WmsStockDump.class, "库存转储单");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:wms_stock_dump:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WmsStockDump.class);
    }

}
