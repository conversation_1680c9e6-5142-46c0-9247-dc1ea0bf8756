package org.jeecg.modules.admin.service;

import org.jeecg.modules.admin.entity.WmsReceivedetail;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.admin.vo.UpdateReceiveDetailPage;
import com.alibaba.fastjson.JSONObject;

import java.util.List;

/**
 * @Description: 收货单据明细
 * @Author: jeecg-boot
 * @Date:   2024-06-14
 * @Version: V1.0
 */
public interface IWmsReceivedetailService extends IService<WmsReceivedetail> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<WmsReceivedetail>
	 */
	public List<WmsReceivedetail> selectByMainId(String mainId);

    void updateReceiveDetail(List<UpdateReceiveDetailPage> updateReceiveDetailPageList);
    
    /**
     * 同步K3信息到收货单据明细
     *
     * @param jsonObj K3信息JSON对象
     */
    void 	syncK3Message(JSONObject jsonObj);
}
