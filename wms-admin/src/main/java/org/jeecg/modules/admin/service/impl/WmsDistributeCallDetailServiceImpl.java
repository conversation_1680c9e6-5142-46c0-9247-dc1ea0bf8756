package org.jeecg.modules.admin.service.impl;

import org.jeecg.modules.admin.entity.WmsDistributeCallDetail;
import org.jeecg.modules.admin.mapper.WmsDistributeCallDetailMapper;
import org.jeecg.modules.admin.service.IWmsDistributeCallDetailService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 分布式调出单明细
 * @Author: jeecg-boot
 * @Date:   2024-11-05
 * @Version: V1.0
 */
@Service
public class WmsDistributeCallDetailServiceImpl extends ServiceImpl<WmsDistributeCallDetailMapper, WmsDistributeCallDetail> implements IWmsDistributeCallDetailService {
	
	@Autowired
	private WmsDistributeCallDetailMapper wmsDistributeCallDetailMapper;
	
	@Override
	public List<WmsDistributeCallDetail> selectByMainId(String mainId) {
		return wmsDistributeCallDetailMapper.selectByMainId(mainId);
	}
}
