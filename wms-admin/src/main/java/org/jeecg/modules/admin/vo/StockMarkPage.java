package org.jeecg.modules.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description: 标记禁用
 * @Author: jeecg-boot
 * @Date:   2024-06-19
 * @Version: V1.0
 */
@Data
@ApiModel(value="StockMarkPage对象", description="标记禁用")
public class StockMarkPage {

	/**库存id*/
	@ApiModelProperty(value = "库存id")
    private String stockID;
	/**货位id*/
	@ApiModelProperty(value = "货位id")
	private String locate_id;
	/**货位编号*/
	@ApiModelProperty(value = "货位编号")
	private String locate_code;
	/**lpn*/
	@ApiModelProperty(value = "lpn")
	private String lpn;
	/**库存状态*/
	@ApiModelProperty(value = "库存状态")
	private String inv_state;
	/**禁用原因*/
	@ApiModelProperty(value = "禁用原因")
	private String forbid_reason;
	/**禁用类型*/
	@ApiModelProperty(value = "禁用类型")
	private String forbidType;
	/**出库明细id*/
	@ApiModelProperty(value = "出库明细id")
	private String ob_dtl_id;
	/**出库口*/
	@ApiModelProperty(value = "出库口")
	private String target_position;
	/**操作人*/
	@ApiModelProperty(value = "操作人")
	private String user;
}
