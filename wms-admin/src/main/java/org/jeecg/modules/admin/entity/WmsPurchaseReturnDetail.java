package org.jeecg.modules.admin.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.util.Date;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.UnsupportedEncodingException;

/**
 * @Description: 采购退货明细
 * @Author: jeecg-boot
 * @Date:   2024-10-26
 * @Version: V1.0
 */
@ApiModel(value="wms_purchase_return_detail对象", description="采购退货明细")
@Data
@TableName("wms_purchase_return_detail")
public class WmsPurchaseReturnDetail implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**序号*/
	@Excel(name = "序号", width = 15)
    @ApiModelProperty(value = "序号")
    private java.lang.Integer serialNumber;
	/**单据号*/
	@Excel(name = "单据号", width = 15)
    @ApiModelProperty(value = "单据号")
    private java.lang.String billNo;
	/**单据ID*/
    @ApiModelProperty(value = "单据ID")
    private java.lang.String billId;
	/**品号*/
	@Excel(name = "品号", width = 15)
    @ApiModelProperty(value = "品号")
    private java.lang.String itemCode;
	/**品名*/
	@Excel(name = "品名", width = 15)
    @ApiModelProperty(value = "品名")
    private java.lang.String itemName;
	/**批号*/
	@Excel(name = "批号", width = 15)
    @ApiModelProperty(value = "批号")
    private java.lang.String batchCode;
	/**规格*/
	@Excel(name = "规格", width = 15)
    @ApiModelProperty(value = "规格")
    private java.lang.String itemSpec;
	/**单位*/
	@Excel(name = "单位", width = 15)
    @ApiModelProperty(value = "单位")
    private java.lang.String itemUnit;
    /**计划退货数量 */
    @Excel(name = "计划退货数量", width = 15)
    @ApiModelProperty(value = "计划退货数量")
    private java.lang.Double planQty;
    /**已配货数量*/
    @Excel(name = "已配货数量", width = 15)
    @ApiModelProperty(value = "已配货数量")
    private java.lang.Double disQty;
    /**实际退货数量*/
	@Excel(name = "实际退货数量", width = 15)
    @ApiModelProperty(value = "实际退货数量")
    private java.lang.Double actQty;
	/**仓库*/
	@Excel(name = "仓库", width = 15)
    @ApiModelProperty(value = "仓库")
    private java.lang.String warehouse;
	/**储存位置*/
	@Excel(name = "储存位置", width = 15)
    @ApiModelProperty(value = "储存位置")
    private java.lang.String locateCode;
	/**物料毛重*/
	@Excel(name = "物料毛重", width = 15)
    @ApiModelProperty(value = "物料毛重")
    private java.lang.Double grossWeight;
	/**物料体积*/
	@Excel(name = "物料体积", width = 15)
    @ApiModelProperty(value = "物料体积")
    private java.lang.Double volume;
	/**计价单位*/
	@Excel(name = "计价单位", width = 15)
    @ApiModelProperty(value = "计价单位")
    private java.lang.String valueUnit;
	/**计价数量*/
	@Excel(name = "计价数量", width = 15)
    @ApiModelProperty(value = "计价数量")
    private java.lang.Double valueQty;
	/**单据明细状态*/
	@Excel(name = "单据明细状态", width = 15, dicCode = "purchase_return_detail_line_state")
	@Dict(dicCode = "purchase_return_detail_line_state")
    @ApiModelProperty(value = "单据明细状态")
    private java.lang.String lineState;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
    /**仓库状态 */
    @Excel(name = "仓库状态", width = 15, dicCode = "warehouse_status")
    @Dict(dicCode = "warehouse_status")
    @ApiModelProperty(value = "仓库状态")
    private java.lang.String warehouseStatus;
}
