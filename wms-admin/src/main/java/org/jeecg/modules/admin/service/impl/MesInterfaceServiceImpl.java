package org.jeecg.modules.admin.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jeecg.common.api.dto.LogDTO;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.admin.entity.MesInterface;
import org.jeecg.modules.admin.mapper.MesInterfaceMapper;
import org.jeecg.modules.admin.service.IMesInterfaceService;
import org.jeecg.modules.base.service.BaseCommonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Description: mes接口管理
 * @Author: jeecg-boot
 * @Date:   2024-12-12
 * @Version: V1.0
 */
@Service
public class MesInterfaceServiceImpl extends ServiceImpl<MesInterfaceMapper, MesInterface> implements IMesInterfaceService {

    private static final Logger log = LoggerFactory.getLogger(MesInterfaceServiceImpl.class);
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private BaseCommonService baseCommonService;

    @Override
    public void performLoginAndStoreToken() throws IOException {
        LoginUser sysUser = new LoginUser();
        sysUser.setUsername("system");
        sysUser.setRealname("系统用户");

        // 1. 查询接口配置信息
        MesInterface mesInterface = this.getOne(
                new QueryWrapper<MesInterface>().eq("interface_code", "JK001")
        );
        if (mesInterface == null) {
            throw new RuntimeException("未找到接口编号为JK001的mes登录接口配置信息！");
        }

        // 2. 判断接口状态是否启用，"1"为启用状态
        if (!"1".equals(mesInterface.getInterfaceStatus())) {
            // 接口未启用则不进行请求
            throw new RuntimeException("mes登录接口为停用状态，无法进行请求");
        }

        String url = mesInterface.getInterfaceUrl();
        String requestMethod = mesInterface.getRequestMethod();
        if (!"post".equalsIgnoreCase(requestMethod)) {
            throw new RuntimeException("登录接口请求方式不是POST，请检查接口配置！");
        }

        // 构建请求体
        JSONObject payload = new JSONObject();
        payload.put("username", "jw");
        payload.put("password", "123456Aa");
        String requestBody = payload.toJSONString();

        long startTime = System.currentTimeMillis(); // 开始时间
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost post = new HttpPost(url);
            post.setHeader("Content-Type", "application/json;charset=utf-8");
            post.setEntity(new StringEntity(requestBody, StandardCharsets.UTF_8));

            try (CloseableHttpResponse response = httpClient.execute(post)) {
                long endTime = System.currentTimeMillis();
                long costTime = endTime - startTime;

                int statusCode = response.getStatusLine().getStatusCode();
                String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

                if (statusCode == 200) {
                    // 使用fastjson解析返回结果
                    JSONObject jsonObject = JSONObject.parseObject(responseBody);
                    int code = jsonObject.getIntValue("code");
                    String msg = jsonObject.getString("msg");
                    JSONObject data = jsonObject.getJSONObject("data");

                    if (code == 200 && data != null) {
                        String accessToken = data.getString("access_token");
                        Integer expiresIn = data.getInteger("expires_in");

                        if (accessToken == null || expiresIn == null) {
                            // access_token或expires_in为空，记录日志
                            log.error("业务请求失败，未获取到access_token或expires_in，请求url：{}，请求参数：{}，返回结果：{}", url, requestBody, responseBody);
                            baseCommonService.addLog(new LogDTO(
                                    "业务请求失败-未获取到token值",
                                    CommonConstant.LOG_TYPE_3,
                                    null,
                                    sysUser,
                                    requestBody,
                                    "POST",
                                    url,
                                    "performLoginAndStoreToken",
                                    responseBody,
                                    GetLocalIpAddress(),
                                    costTime
                            ));
                            throw new RuntimeException("登录接口返回的数据中无access_token或expires_in字段！");
                        }

                        // 存入Redis
                        String redisKey = mesInterface.getRedisKey();
                        if (redisKey == null || redisKey.trim().isEmpty()) {
                            redisKey = "mes_login_access_token";
                        } else {
                            redisKey = redisKey + "_access_token";
                        }

                        // 设置过期时间（expiresIn秒）
                        redisTemplate.opsForValue().set(redisKey, accessToken, expiresIn, TimeUnit.SECONDS);

                        log.info("获取并存储access_token成功，Redis Key: {}，expires_in: {}秒, 耗时: {}ms", redisKey, expiresIn, costTime);

                    } else {
                        // code != 200 或 data为空
                        log.error("业务请求失败，状态码：{}，请求url：{}，请求参数：{}，返回结果：{}", code, url, requestBody, responseBody);
                        baseCommonService.addLog(new LogDTO(
                                "业务请求失败-登录接口非200状态",
                                CommonConstant.LOG_TYPE_3,
                                null,
                                sysUser,
                                requestBody,
                                "POST",
                                url,
                                "performLoginAndStoreToken",
                                responseBody,
                                GetLocalIpAddress(),
                                costTime
                        ));
                        throw new RuntimeException("调用mes登录接口返回非200状态码或data为空，code=" + code);
                    }
                } else {
                    // 非200状态码
                    log.error("网络请求失败，请求url：{}，请求参数：{}，状态码：{}，返回结果：{}", url, requestBody, statusCode, responseBody);
                    baseCommonService.addLog(new LogDTO(
                            "网络请求失败-performLoginAndStoreToken",
                            CommonConstant.LOG_TYPE_3,
                            null,
                            sysUser,
                            requestBody,
                            "POST",
                            url,
                            "performLoginAndStoreToken",
                            "请求失败，状态码：" + statusCode + "，响应：" + responseBody,
                            GetLocalIpAddress(),
                            costTime
                    ));
                    throw new RuntimeException("调用mes登录接口失败，HTTP Status: " + statusCode + ", Response: " + responseBody);
                }
            }
        } catch (IOException e) {
            long endTime = System.currentTimeMillis();
            long costTime = endTime - startTime;
            // IO异常，记录日志
            log.error("请求失败，发生IOException：{}", e.getMessage());
            baseCommonService.addLog(new LogDTO(
                    "业务请求失败-调用mes登录接口IO异常"+e.getMessage(),
                    CommonConstant.LOG_TYPE_3,
                    null,
                    sysUser,
                    payload.toJSONString(),
                    "POST",
                    mesInterface.getInterfaceUrl(),
                    "performLoginAndStoreToken",
                    "发生IOException：" + e.getMessage(),
                    GetLocalIpAddress(),
                    costTime
            ));
            throw new IOException("调用mes登录接口时发生IO异常", e);
        }
    }

    private String GetLocalIpAddress() {
        // 根据您之前的实现方式
        try {
            InetAddress localHost = InetAddress.getLocalHost();
            return localHost.getHostAddress();
        } catch (UnknownHostException e) {
            return "未知";
        }
    }

    @Override
    public void updatePrefix(String prefix) {
        log.info("开始一键修改所有接口前缀，新前缀：{}", prefix);
        
        // 获取所有接口数据
        List<MesInterface> interfaces = this.list();
        if (interfaces == null || interfaces.isEmpty()) {
            log.warn("未找到任何接口数据，无法进行前缀修改");
            return;
        }
        
        int updatedCount = 0;
        
        for (MesInterface mesInterface : interfaces) {
            String oldUrl = mesInterface.getInterfaceUrl();
            if (oldUrl == null || oldUrl.trim().isEmpty()) {
                log.warn("接口 [{}] - [{}] URL为空，跳过处理", mesInterface.getInterfaceCode(), mesInterface.getInterfaceName());
                continue;
            }
            
            String newUrl;
            if (oldUrl.contains("/stage-api")) {
                // 查找 "/stage-api" 所在的位置
                int stageApiIndex = oldUrl.indexOf("/stage-api");
                if (stageApiIndex > 0) {
                    // 将前面的部分替换为新前缀，保留 "/stage-api" 及其后面的部分
                    newUrl = prefix + oldUrl.substring(stageApiIndex);
                } else {
                    // 这种情况不太可能发生，因为已经确认包含 "/stage-api"
                    newUrl = oldUrl;
                }
            } else {
                // 不包含 "/stage-api"，尝试一般性的处理
                // 查找第三个斜杠的位置 (协议://域名/之后的第一个斜杠)
                int protocolEnd = oldUrl.indexOf("://");
                if (protocolEnd > 0) {
                    int domainEnd = oldUrl.indexOf("/", protocolEnd + 3);
                    if (domainEnd > 0) {
                        // 替换协议和域名部分，保留路径部分
                        newUrl = prefix + oldUrl.substring(domainEnd);
                    } else {
                        // 如果URL格式不包含路径部分，保持不变
                        newUrl = oldUrl;
                        log.warn("接口 [{}] - [{}] URL格式异常：{}，保持不变", 
                                mesInterface.getInterfaceCode(), mesInterface.getInterfaceName(), oldUrl);
                        continue;
                    }
                } else {
                    // 如果URL格式不包含协议部分，直接尝试替换
                    if (oldUrl.startsWith("/")) {
                        // 如果URL已经是相对路径，直接添加前缀
                        newUrl = prefix + oldUrl;
                    } else {
                        // 否则添加斜杠后添加前缀
                        newUrl = prefix + "/" + oldUrl;
                    }
                }
            }
            
            // 如果URL有变化，则更新
            if (!oldUrl.equals(newUrl)) {
                log.info("接口 [{}] - [{}] URL从 [{}] 更新为 [{}]", 
                        mesInterface.getInterfaceCode(), mesInterface.getInterfaceName(), oldUrl, newUrl);
                
                mesInterface.setInterfaceUrl(newUrl);
                this.updateById(mesInterface);
                updatedCount++;
            }
        }
        
        log.info("接口前缀修改完成，共处理 {} 个接口，更新 {} 个接口", interfaces.size(), updatedCount);
    }
}
