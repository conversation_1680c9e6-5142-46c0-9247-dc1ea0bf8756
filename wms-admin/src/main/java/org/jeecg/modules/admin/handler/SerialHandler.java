package org.jeecg.modules.admin.handler;

import org.jeecg.modules.admin.service.IWmsSerialNumService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Component
public class SerialHandler {

    @Resource
    private IWmsSerialNumService wmsSerialNumService;

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getSerialNo(String serialType) {
        return wmsSerialNumService.getSerialNo(serialType);
    }
}
