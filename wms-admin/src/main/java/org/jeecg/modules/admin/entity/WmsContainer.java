package org.jeecg.modules.admin.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 托盘表
 * @Author: jeecg-boot
 * @Date:   2024-11-27
 * @Version: V1.0
 */
@Data
@TableName("wms_container")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="wms_container对象", description="托盘表")
public class WmsContainer implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**托盘条码*/
	@Excel(name = "托盘条码", width = 15)
    @ApiModelProperty(value = "托盘条码")
    private java.lang.String containerBarcode;
	/**托盘类型*/
	@Excel(name = "托盘类型", width = 15, dicCode = "container_type")
	@Dict(dicCode = "container_type")
    @ApiModelProperty(value = "托盘类型")
    private java.lang.String containerType;
	/**区域编号*/
	@Excel(name = "区域编号", width = 15)
    @ApiModelProperty(value = "区域编号")
    private java.lang.String zoneCode;
	/**批号*/
	@Excel(name = "批号", width = 15)
    @ApiModelProperty(value = "批号")
    private java.lang.String batchCode;
	/**数量*/
	@Excel(name = "数量", width = 15, type = 4)
    @ApiModelProperty(value = "数量")
    private java.lang.Double quantity;
	/**是否满托*/
	@Excel(name = "是否满托", width = 15, dicCode = "yn")
	@Dict(dicCode = "yn")
    @ApiModelProperty(value = "是否满托")
    private java.lang.String isFull;
	/**使用次数*/
	@Excel(name = "使用次数", width = 15, type = 4)
    @ApiModelProperty(value = "使用次数")
    private java.lang.Integer useCount;
	/**存货时间*/
	@Excel(name = "存货时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "存货时间")
    private java.util.Date storageTime;
	/**托盘采购日期*/
	@Excel(name = "托盘采购日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "托盘采购日期")
    private java.util.Date purchaseTime;
	/**托盘材质*/
	@Excel(name = "托盘材质", width = 15)
    @ApiModelProperty(value = "托盘材质")
    private java.lang.String material;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
}
