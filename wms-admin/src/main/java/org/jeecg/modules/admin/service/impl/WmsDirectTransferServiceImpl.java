package org.jeecg.modules.admin.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jeecg.common.api.dto.LogDTO;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.admin.entity.MesInterface;
import org.jeecg.modules.admin.entity.WmsDirectTransfer;
import org.jeecg.modules.admin.entity.WmsStockdetail;
import org.jeecg.modules.admin.mapper.WmsDirectTransferMapper;
import org.jeecg.modules.admin.service.IMesInterfaceService;
import org.jeecg.modules.admin.service.IWmsApiService;
import org.jeecg.modules.admin.service.IWmsDirectTransferService;
import org.jeecg.modules.admin.service.IWmsStockdetailService;
import org.jeecg.modules.admin.vo.OutSelectPage;
import org.jeecg.modules.admin.vo.OutSendPage;
import org.jeecg.modules.admin.vo.StockPage;
import org.jeecg.modules.base.service.BaseCommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import org.apache.shiro.SecurityUtils;
import org.jeecg.modules.admin.util.HttpClientUtil;

/**
 * @Description: 直接调拨单
 * @Author: jeecg-boot
 * @Date:   2024-11-04
 * @Version: V1.0
 */
@Log4j2
@Service
public class WmsDirectTransferServiceImpl extends ServiceImpl<WmsDirectTransferMapper, WmsDirectTransfer> implements IWmsDirectTransferService {
    @Autowired
    private WmsDirectTransferMapper wmsDirectTransferMapper;
    @Autowired
    private IMesInterfaceService mesInterfaceService;
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private BaseCommonService baseCommonService;
    @Autowired
    private IWmsStockdetailService wmsStockdetailService;
    @Autowired
    private IWmsApiService wmsApiService;
    @Override
    public JSONObject submitRecord(String id,boolean b) {
        // TODO Auto-generated method stub
        log.info("开始提交直接调拨单数据，id:{}", id);
        //根据id查询数据
        WmsDirectTransfer wmsDirectTransfer = wmsDirectTransferMapper.selectById(id);
        if (wmsDirectTransfer == null) {
            throw new RuntimeException("直接调拨单不存在，id:" + id);
        }
        // 根据单据状态判断是否可以提交
        if (!"4".equals(wmsDirectTransfer.getBillStatus())) {
            throw new RuntimeException("库存调拨单为非完成状态，无法提交！");
        }
        // 根据erpSync判断是否已同步
        if ("2".equals(wmsDirectTransfer.getErpSync())) {
            throw new RuntimeException("直接调拨单已同步，id:" + id);
        }
        MesInterface mi = mesInterfaceService.getOne(new QueryWrapper<MesInterface>().eq("interface_code", "JK022"));
        if (mi == null) {
            throw new RuntimeException("接口配置不存在:JK022");
        }
        // 校验启用状态 & 请求方式
        if (!"1".equals(mi.getInterfaceStatus())) {
            throw new RuntimeException("接口JK022未启用！");
        }
        if (!"POST".equalsIgnoreCase(mi.getRequestMethod())) {
            throw new RuntimeException("接口JK022的请求方式不是POST！");
        }
        // 获取token
        String accessToken = redisTemplate.opsForValue().get("mes_login_access_token");
        if (accessToken == null) {
            // 如果没有获取到MES登录token，则调用performLoginAndStoreToken方法获取
            try {
                log.info("未获取到MES登录token，正在调用登录接口获取...");
                mesInterfaceService.performLoginAndStoreToken();
                // 重新获取token
                accessToken = redisTemplate.opsForValue().get("mes_login_access_token");
                if (accessToken == null) {
                    throw new RuntimeException("登录接口调用成功但仍未获取到MES登录token！");
                }
                log.info("已成功获取MES登录token");
            } catch (IOException e) {
                throw new RuntimeException("调用MES登录接口时发生异常: " + e.getMessage(), e);
            }
        }
        //构建请求参数
        JSONObject requestParam = buildRequestParam(wmsDirectTransfer,b);
        //调用接口 doPost
        // 7. 发起接口请求
        JSONObject jsonObject = doPost(mi.getInterfaceUrl(), accessToken, requestParam);
        //处理相应
        return handleResponse(jsonObject, wmsDirectTransfer);
    }

    /**
     * 处理接口返回JSON，若成功更新数据库，若失败抛出异常或返回提示信息
     *
     * @param responseJson  接口返回结果
     * @param wmsDirectTransfer 当前调拨单实体
     * @return 处理后的 JSON 结果
     */
    private JSONObject handleResponse(JSONObject responseJson, WmsDirectTransfer wmsDirectTransfer) {
        JSONObject result = new JSONObject();

        // 统一接收的几个关键字段
        boolean isSuccess;
        int msgCode;
        JSONArray errors = null;
        String k3Number = null;  // 成功时可能会有 K3 单号

        // 1. 判断是否有 "Result" 节点
        JSONObject resultNode = responseJson.getJSONObject("Result");
        if (resultNode != null) {
            // 1.1 如果有，则从 "Result" -> "ResponseStatus" 中解析
            JSONObject responseStatus = resultNode.getJSONObject("ResponseStatus");
            if (responseStatus == null) {
                throw new RuntimeException("提交直接调拨单失败，无ResponseStatus节点！");
            }
            isSuccess = responseStatus.getBooleanValue("IsSuccess");
            msgCode = responseStatus.getIntValue("MsgCode");
            errors = responseStatus.getJSONArray("Errors");
            k3Number = resultNode.getString("Number");
        } else {
            // 1.2 如果没有 "Result" 节点，则视为「请求失败」结构，直接从顶层解析
            isSuccess = responseJson.getBooleanValue("isSuccess");
            msgCode = responseJson.getIntValue("msgCode");
            errors = responseJson.getJSONArray("errors");
            k3Number = null;
        }

        // 2. 根据解析结果组装错误消息
        String errorMessage = (errors != null && !errors.isEmpty())
                ? errors.toJSONString()
                : "未知错误";

        // 3. 根据 isSuccess 进行后续处理
        if (isSuccess) {
            // 成功
            wmsDirectTransfer.setWorkNo(k3Number);
            wmsDirectTransfer.setErpSync("2"); // "2"表示已同步
            wmsDirectTransferMapper.updateById(wmsDirectTransfer);

            log.info("直接调拨单同步成功, ID: {}, K3单号: {}", wmsDirectTransfer.getId(), k3Number);
            result.put("success", true);
            result.put("message", "直接调拨单同步成功");
            result.put("k3Number", k3Number);
        } else {
            // 失败：根据 msgCode 进一步区分
            if (msgCode == 11) {
                // 依然是失败，但告诉前端可以进行"强制提交"的选择
                // message 不再写死"库存不足"，而是使用后端实际报错信息
                result.put("success", false);
                result.put("forceSubmit", true);
                result.put("message", errorMessage);
            } else {
                // 其他错误
                log.error("直接调拨单同步失败, ID: {}, 错误信息: {}", wmsDirectTransfer.getId(), errorMessage);
                result.put("success", false);
                result.put("message", "直接调拨单同步失败: " + errorMessage);
            }
            // 同步状态更新为失败
            wmsDirectTransfer.setErpSync("3");
            wmsDirectTransferMapper.updateById(wmsDirectTransfer);
        }

        return result;
    }



    private JSONObject buildRequestParam(WmsDirectTransfer wmsDirectTransfer, boolean ignoreInterationFlag) {
        //获取当前登录用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        JSONObject requestParam = new JSONObject();
        // 1. date（格式：yyyy-MM-dd）
        String dateStr = "";
        if (wmsDirectTransfer.getTransferDate() != null) {
            dateStr = new SimpleDateFormat("yyyy-MM-dd").format(wmsDirectTransfer.getTransferDate());
        }
        requestParam.put("date", dateStr);

        // 2. ignoreInterationFlag
        requestParam.put("ignoreInterationFlag", ignoreInterationFlag);
        requestParam.put("ftransferdirect", "GENERAL");       // GENERAL or RETURN
        requestParam.put("ftransferbiztype", "InnerOrgTransfer");
        requestParam.put("fownertypeoutidhead", "BD_OwnerOrg");
        requestParam.put("creator", loginUser.getUsername());
        requestParam.put("fstockernumber", "");       // 仓管员编码（非必填）
        requestParam.put("fstockergroupnumber", "");  // 库存组编码（非必填）
        requestParam.put("fbijhyfs", "");             // 货运方式（非必填）
        requestParam.put("fbijsjnumber", "");         // 短运司机
        requestParam.put("fbijhylxnumber", "");       // 货运路线
        requestParam.put("fbijwlgsnumber", "");       // 货运公司
        requestParam.put("fnote", wmsDirectTransfer.getRemark() == null ? "" : wmsDirectTransfer.getRemark());

        // 5. list 数组构建
        JSONArray detailList = new JSONArray();

        JSONObject detailItem = new JSONObject();
        // fmaterialnumber 物料编号
        detailItem.put("fmaterialnumber", wmsDirectTransfer.getItemCode());
        // fbijauxqty 调拨数量_实物
        detailItem.put("fbijauxqty", wmsDirectTransfer.getTransferQty());
        // fbijperrate 含量（默认填1,表示100%）
        detailItem.put("fbijperrate", 1);
        // fqty 调拨数量
        detailItem.put("fqty", wmsDirectTransfer.getTransferQty());
        // fsrcstocknumber 调出仓库
        detailItem.put("fsrcstocknumber", wmsDirectTransfer.getWarehouseCodeOut());
        // fdeststocknumber 调入仓库
        detailItem.put("fdeststocknumber", wmsDirectTransfer.getWarehouseCodeIn());
        // fextauxunitqty 调拨数量（辅单位）
        detailItem.put("fextauxunitqty", wmsDirectTransfer.getTransferQty());

        detailList.add(detailItem);
        requestParam.put("list", detailList);

        return requestParam;
    }


    /**
     * 通用的POST请求(示例). 若已有可复用的doPost，请在此替换即可
     */
    private JSONObject doPost(String url, String token, JSONObject requestBody) {
        return HttpClientUtil.doPostEnhanced(url, token, requestBody, baseCommonService, "MES直接调拨", "syncDirectTransferData");
    }

    private String getLocalIpAddress() {
        try {
            InetAddress localHost = InetAddress.getLocalHost();
            return localHost.getHostAddress();
        } catch (UnknownHostException e) {
            return "未知";
        }
    }

    @Override
    public JSONObject outRecord(String id) {
        // 1. 获取直接调拨单
        WmsDirectTransfer wmsDirectTransfer = wmsDirectTransferMapper.selectById(id);
        if (wmsDirectTransfer == null) {
            throw new RuntimeException("未找到对应的直接调拨单，ID = " + id);
        }

        // 2. 校验 stockId
        String stockId = wmsDirectTransfer.getStockId();
        if (StringUtils.isBlank(stockId)) {
            throw new RuntimeException("对应的 StockId 为空，无法进行调拨");
        }

        // 3. 根据 stockId 查询库存信息
        WmsStockdetail wmsStockdetail = wmsStockdetailService.getById(stockId);
        if (wmsStockdetail == null) {
            throw new RuntimeException("未找到对应的库存信息，StockId = " + stockId);
        }

        // 4. 组装出库请求参数
        OutSelectPage outSelectPage = new OutSelectPage();
        outSelectPage.setDetailID(wmsDirectTransfer.getId());
        outSelectPage.setBill_type("ZJDB");
        outSelectPage.setOutstation("A007");

        // 构建 StockPage 并加入列表
        StockPage stockPage = new StockPage();
        stockPage.setStockID(wmsStockdetail.getId());
        stockPage.setLocate_id(wmsStockdetail.getLocateId());
        stockPage.setLocate_code(wmsStockdetail.getLocateCode());
        stockPage.setLpn(wmsStockdetail.getLpn());
        stockPage.setInv_state(wmsStockdetail.getInvState());
        stockPage.setSort_station("Q001");
        stockPage.setQuantity(wmsStockdetail.getQuantity());

        List<StockPage> pageList = new ArrayList<>();
        pageList.add(stockPage);

        outSelectPage.setProStockList(pageList);

        // 5. 调用出库选择接口
        JSONObject jsonObject = wmsApiService.proOutSelect(outSelectPage);
        if (!Boolean.TRUE.equals(jsonObject.getBoolean("result"))) {
            throw new RuntimeException("库存检查失败，无法发货，StockId = " + stockId);
        }

        // 6. 组装正式出库请求参数
        OutSendPage outSendPage = new OutSendPage();
        outSendPage.setBillID(wmsDirectTransfer.getId());
        outSendPage.setBill_type("ZJDB");

        OutSendPage.sendDetailPage sendDetailPage = new OutSendPage.sendDetailPage();
        sendDetailPage.setDetailID(wmsDirectTransfer.getId());
        sendDetailPage.setLine_state(wmsDirectTransfer.getBillStatus());

        List<OutSendPage.sendDetailPage> sendDetailList = new ArrayList<>();
        sendDetailList.add(sendDetailPage);
        outSendPage.setDetailList(sendDetailList);

        // 7. 调用正式出库接口
        JSONObject jsonObject1 = wmsApiService.proOutSend(outSendPage);
        if (!Boolean.TRUE.equals(jsonObject1.getBoolean("result"))) {
            throw new RuntimeException("出库操作失败，直接调拨单 ID = " + id);
        }

        // 8. 全部成功后返回最终结果
        return jsonObject1;
    }

}
