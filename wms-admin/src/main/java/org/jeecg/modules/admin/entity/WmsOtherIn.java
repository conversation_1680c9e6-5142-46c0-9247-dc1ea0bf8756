package org.jeecg.modules.admin.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 其他入库单
 * @Author: jeecg-boot
 * @Date:   2024-11-04
 * @Version: V1.0
 */
@Data
@TableName("wms_other_in")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="wms_other_in对象", description="其他入库单")
public class WmsOtherIn implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**单据号*/
	@Excel(name = "单据号", width = 15)
    @ApiModelProperty(value = "单据号")
    private java.lang.String billNo;
	/**K3单号*/
	@Excel(name = "K3单号", width = 15)
    @ApiModelProperty(value = "K3单号")
    private java.lang.String workNo;
	/**单据状态*/
	@Excel(name = "单据状态", width = 15, dicCode = "wms_other_in_bill_status")
	@Dict(dicCode = "wms_other_in_bill_status")
    @ApiModelProperty(value = "单据状态")
    private java.lang.String billStatus;
	/**品号*/
	@Excel(name = "品号", width = 15)
    @ApiModelProperty(value = "品号")
    private java.lang.String itemCode;
	/**品名*/
	@Excel(name = "品名", width = 15)
    @ApiModelProperty(value = "品名")
    private java.lang.String itemName;
	/**规格*/
	@Excel(name = "规格", width = 15)
    @ApiModelProperty(value = "规格")
    private java.lang.String itemSpec;
	/**单位*/
	@Excel(name = "单位", width = 15)
    @ApiModelProperty(value = "单位")
    private java.lang.String itemUnit;
	/**收货仓库编号*/
	@Excel(name = "收货仓库编号", width = 15)
    @ApiModelProperty(value = "收货仓库编号")
    private java.lang.String warehouseCodeIn;
	/**收货仓库名称*/
	@Excel(name = "收货仓库名称", width = 15)
    @ApiModelProperty(value = "收货仓库名称")
    private java.lang.String warehouseIn;
	/**实收数量*/
	@Excel(name = "实收数量", width = 15)
    @ApiModelProperty(value = "实收数量")
    private java.lang.Double receiveQty;
	/**日期*/
	@Excel(name = "日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "日期")
    private java.util.Date receiveDate;
	/**单件重量*/
	@Excel(name = "单件重量", width = 15)
    @ApiModelProperty(value = "单件重量")
    private java.lang.Double singleWeight;
	/**ERP同步状态*/
	@Excel(name = "ERP同步状态", width = 15, dicCode = "erp_sync")
	@Dict(dicCode = "erp_sync")
    @ApiModelProperty(value = "ERP同步状态")
    private java.lang.String erpSync;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
}
