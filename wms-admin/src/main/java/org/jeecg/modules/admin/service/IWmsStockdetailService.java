package org.jeecg.modules.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.jeecg.modules.admin.entity.WmsStockdetail;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * @Description: 库存表
 * @Author: jeecg-boot
 * @Date:   2024-06-17
 * @Version: V1.0
 */
public interface IWmsStockdetailService extends IService<WmsStockdetail> {

    IPage<WmsStockdetail> listSummary(WmsStockdetail wmsStockdetail, Integer pageNo, Integer pageSize, HttpServletRequest req);

    List<Map<String, Object>> getTop5();
    Map<String, Object> getWeekStock(String startTime, String endTime);

    List<WmsStockdetail> queryWithNullObDtlId(QueryWrapper<WmsStockdetail> queryWrapper,String levelNo);

    List<WmsStockdetail> queryWithNullObDtlIdForFlat(QueryWrapper<WmsStockdetail> queryWrapper);

    Map<String, Object> getWarehouseAge();
    
    /**
     * 按照品号、品名、批号、区域相同的条件汇总库存
     * @param wmsStockdetail 查询条件
     * @param pageNo 页码
     * @param pageSize 每页大小
     * @param req 请求参数
     * @return 分页结果
     */
    IPage<WmsStockdetail> queryMaterialSummary(WmsStockdetail wmsStockdetail, Integer pageNo, Integer pageSize, HttpServletRequest req);
    
    /**
     * 查询库存预警信息
     * 包括库存不足和库存积压的物料
     * @return 库存预警列表
     */
    List<Map<String, Object>> queryStockAlert();
    
    /**
     * 发送库存预警信息
     * 该方法会查询库存预警信息并发送系统消息
     * @return 是否有预警信息被发送
     */
    boolean sendStockAlert();
}
