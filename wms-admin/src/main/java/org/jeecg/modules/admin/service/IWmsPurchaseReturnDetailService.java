package org.jeecg.modules.admin.service;

import org.jeecg.modules.admin.entity.WmsPurchaseReturnDetail;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 采购退货明细
 * @Author: jeecg-boot
 * @Date:   2024-10-26
 * @Version: V1.0
 */
public interface IWmsPurchaseReturnDetailService extends IService<WmsPurchaseReturnDetail> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<WmsPurchaseReturnDetail>
	 */
	public List<WmsPurchaseReturnDetail> selectByMainId(String mainId);
}
