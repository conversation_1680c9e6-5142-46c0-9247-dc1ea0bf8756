package org.jeecg.modules.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.admin.constant.WmsConstant;
import org.jeecg.modules.admin.service.IWmsApiService;
import org.jeecg.modules.admin.vo.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class WmsApiServiceImpl implements IWmsApiService {

    @Value("${wcs.url}")
    private String BASE_URL;
    //private final String BASE_URL = "http://192.168.70.14:8010";

    private JSONObject executeRequest(String url, Object requestObject) {
        JSONObject result = new JSONObject();
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost post = new HttpPost(url);
            post.setHeader("Content-Type", "application/json");
            System.out.println(requestObject.toString());

            // 获取当前登录用户信息并设置UserName请求头
            try {
                LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
                if (loginUser != null && loginUser.getUsername() != null) {
                    post.setHeader("UserName", loginUser.getUsername());
                }
            } catch (Exception e) {
                log.warn("获取当前登录用户信息失败: {}", e.getMessage());
            }

            String requestBody = JSON.toJSONString(requestObject);
            post.setEntity(new StringEntity(requestBody));

            try (CloseableHttpResponse response = httpClient.execute(post)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                if (response.getStatusLine().getStatusCode() == 200) {
                    JSONObject jsonObject = JSON.parseObject(responseBody);
                    int code = jsonObject.getIntValue("Code");
                    String message = jsonObject.getString("Message");
                    result.put("message", message);
                    if (WmsConstant.WmsApiResultEnum.SUCCESS.getValue().equals(String.valueOf(code))) {
                        result.put("result", true);
                    } else {
                        result.put("result", false);
                    }
                } else {
                    result.put("result", false);
                    result.put("message", "Request failed: " + response.getStatusLine().getStatusCode() + ". Response: " + responseBody);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
            result.put("result", false);
            result.put("message", "IOException occurred: " + e.getMessage());
        }
        return result;
    }


    /**
     * 执行GET请求
     * @param url 请求URL
     * @return 响应结果
     */
    private String executeGetRequest(String url) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet(url);
            httpGet.setHeader("Content-Type", "application/json");

            // 获取当前登录用户信息并设置UserName请求头
            try {
                LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
                if (loginUser != null && loginUser.getUsername() != null) {
                    httpGet.setHeader("UserName", loginUser.getUsername());
                }
            } catch (Exception e) {
                log.warn("获取当前登录用户信息失败: {}", e.getMessage());
            }

            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                if (response.getStatusLine().getStatusCode() == 200) {
                    return responseBody;
                } else {
                    log.error("GET request failed: {} - {}", response.getStatusLine().getStatusCode(), responseBody);
                    return null;
                }
            }
        } catch (IOException e) {
            log.error("IOException occurred during GET request: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public JSONObject proOutSelect(OutSelectPage outSelectPage) {
        String url = BASE_URL + "/ProOutSelect";
        return executeRequest(url, outSelectPage);
    }

    @Override
    public JSONObject proOutSend(OutSendPage outSendPage) {
        String url = BASE_URL + "/ProOutSend";
        return executeRequest(url, outSendPage);
    }

    @Override
    public JSONObject proOutCancelSelect(OutSelectPage outSelectPage) {
        String url = BASE_URL + "/ProOutCancelSelect";
        return executeRequest(url, outSelectPage);
    }

    @Override
    public JSONObject stockMark(StockMarkPage stockMarkPage) {
        String url = BASE_URL + "/StockMark";
        return executeRequest(url, stockMarkPage);
    }

    @Override
    public JSONObject stockCancel(StockMarkPage stockMarkPage) {
        String url = BASE_URL + "/StockCancel";
        return executeRequest(url, stockMarkPage);
    }

    @Override
    public JSONObject stoForceOut(StoForceOutPage stoForceOutPage) {
        String url = BASE_URL + "/StoForceOut";
        return executeRequest(url, stoForceOutPage);
    }

    @Override
    public JSONObject taskForceDone(TaskForceDonePage taskForceDonePage) {
        String url = BASE_URL + "/TaskForceDone";
        return executeRequest(url, taskForceDonePage);
    }

    @Override
    public JSONObject agvTaskForceDone(AgvTaskForceDonePage agvTaskForceDonePage) {
        String url = BASE_URL + "/AgvComplish";
        return executeRequest(url, agvTaskForceDonePage);
    }

    @Override
    public JSONObject taskPriority(TaskPriorityPage taskPriorityPage) {
        String url = BASE_URL + "/TaskPriority";
        return executeRequest(url, taskPriorityPage);
    }

    @Override
    public JSONObject rawOutSelect(OutSelectPage outSelectPage) {
        String url = BASE_URL + "/RawOutSelect";
        return executeRequest(url, outSelectPage);
    }

    @Override
    public JSONObject rawOutSend(OutSendPage outSendPage) {
        String url = BASE_URL + "/RawOutSend";
        return executeRequest(url, outSendPage);
    }

    @Override
    public JSONObject stockCheckSend(OutCheckPage outCheckPage) {
        String url = BASE_URL + "/CountPlanSend";
        return executeRequest(url, outCheckPage);
    }

    @Override
    public JSONObject proOutSendWithStorage(OutSendPage outSendPage) {
        String url = BASE_URL + "/ProOutSendWithStorage";
        return executeRequest(url, outSendPage);
    }

    @Override
    public JSONObject handleStock(String ids,String bill_type) {
        String url = BASE_URL + "/HandleStock";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("ids", ids);
        jsonObject.put("bill_type",bill_type);
        System.out.println(jsonObject.toJSONString());
        return executeRequest(url, jsonObject);
    }



    @Override
    public List<AgvStatusVO> getAgvStatus() {
        String url = BASE_URL + "/WMS/GetAgvStatus";
        String responseBody = executeGetRequest(url);
        List<AgvStatusVO> result = new ArrayList<>();

        if (responseBody != null) {
            try {
                JSONArray jsonArray = JSON.parseArray(responseBody);
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    AgvStatusVO agvStatusVO = JSON.toJavaObject(jsonObject, AgvStatusVO.class);
                    result.add(agvStatusVO);
                }
            } catch (Exception e) {
                log.error("解析AGV状态响应失败: {}", e.getMessage(), e);
            }
        }

        return result;
    }

    @Override
    public List<RgvStatusVO> getRgvStatus() {
        String url = BASE_URL + "/WMS/GetRgvStatus";
        String responseBody = executeGetRequest(url);
        List<RgvStatusVO> result = new ArrayList<>();

        if (responseBody != null) {
            try {
                JSONArray jsonArray = JSON.parseArray(responseBody);
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    RgvStatusVO rgvStatusVO = JSON.toJavaObject(jsonObject, RgvStatusVO.class);
                    result.add(rgvStatusVO);
                }
            } catch (Exception e) {
                log.error("解析四向车状态响应失败: {}", e.getMessage(), e);
            }
        }

        return result;
    }

    @Override
    public List<HoisterStatusVO> getHoisterStatus() {
        String url = BASE_URL + "/WMS/GetHoisterStatus";
        String responseBody = executeGetRequest(url);
        List<HoisterStatusVO> result = new ArrayList<>();

        if (responseBody != null) {
            try {
                JSONArray jsonArray = JSON.parseArray(responseBody);
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    HoisterStatusVO hoisterStatusVO = JSON.toJavaObject(jsonObject, HoisterStatusVO.class);
                    result.add(hoisterStatusVO);
                }
            } catch (Exception e) {
                log.error("解析提升机状态响应失败: {}", e.getMessage(), e);
            }
        }

        return result;
    }

    @Override
    public JSONObject stockCancelDistribution(List<StockCancelPage> stockCancelPages) {
        String url = BASE_URL + "/StockCancelDistribution";
        JSONObject body = new JSONObject();
        body.put("ProStockList", stockCancelPages);
        return executeRequest(url, body);
    }
}
