package org.jeecg.modules.admin.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.entity.WmsMaterial;
import org.jeecg.modules.admin.service.IWmsMaterialService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 主物料表
 * @Author: jeecg-boot
 * @Date:   2024-12-26
 * @Version: V1.0
 */
@Api(tags="主物料表")
@RestController
@RequestMapping("/admin/wmsMaterial")
@Slf4j
public class WmsMaterialController extends JeecgController<WmsMaterial, IWmsMaterialService> {
	@Autowired
	private IWmsMaterialService wmsMaterialService;
	
	/**
	 * 分页列表查询
	 *
	 * @param wmsMaterial
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "主物料表-分页列表查询")
	@ApiOperation(value="主物料表-分页列表查询", notes="主物料表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WmsMaterial>> queryPageList(WmsMaterial wmsMaterial,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WmsMaterial> queryWrapper = QueryGenerator.initQueryWrapper(wmsMaterial, req.getParameterMap());
		Page<WmsMaterial> page = new Page<WmsMaterial>(pageNo, pageSize);
		IPage<WmsMaterial> pageList = wmsMaterialService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param wmsMaterial
	 * @return
	 */
	@AutoLog(value = "主物料表-添加")
	@ApiOperation(value="主物料表-添加", notes="主物料表-添加")
	@RequiresPermissions("admin:wms_material:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WmsMaterial wmsMaterial) {
		wmsMaterialService.save(wmsMaterial);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param wmsMaterial
	 * @return
	 */
	@AutoLog(value = "主物料表-编辑")
	@ApiOperation(value="主物料表-编辑", notes="主物料表-编辑")
	@RequiresPermissions("admin:wms_material:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WmsMaterial wmsMaterial) {
		wmsMaterialService.updateById(wmsMaterial);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "主物料表-通过id删除")
	@ApiOperation(value="主物料表-通过id删除", notes="主物料表-通过id删除")
	@RequiresPermissions("admin:wms_material:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		wmsMaterialService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "主物料表-批量删除")
	@ApiOperation(value="主物料表-批量删除", notes="主物料表-批量删除")
	@RequiresPermissions("admin:wms_material:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.wmsMaterialService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "主物料表-通过id查询")
	@ApiOperation(value="主物料表-通过id查询", notes="主物料表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WmsMaterial> queryById(@RequestParam(name="id",required=true) String id) {
		WmsMaterial wmsMaterial = wmsMaterialService.getById(id);
		if(wmsMaterial==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(wmsMaterial);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param wmsMaterial
    */
    @RequiresPermissions("admin:wms_material:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WmsMaterial wmsMaterial) {
        return super.exportXls(request, wmsMaterial, WmsMaterial.class, "主物料表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:wms_material:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WmsMaterial.class);
    }

}
