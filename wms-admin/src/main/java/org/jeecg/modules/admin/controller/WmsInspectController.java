package org.jeecg.modules.admin.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.entity.WmsInspect;
import org.jeecg.modules.admin.service.IWmsInspectService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 检验记录
 * @Author: jeecg-boot
 * @Date:   2025-02-20
 * @Version: V1.0
 */
@Api(tags="检验记录")
@RestController
@RequestMapping("/admin/wmsInspect")
@Slf4j
public class WmsInspectController extends JeecgController<WmsInspect, IWmsInspectService> {
	@Autowired
	private IWmsInspectService wmsInspectService;
	
	/**
	 * 分页列表查询
	 *
	 * @param wmsInspect
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "检验记录-分页列表查询")
	@ApiOperation(value="检验记录-分页列表查询", notes="检验记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WmsInspect>> queryPageList(WmsInspect wmsInspect,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WmsInspect> queryWrapper = QueryGenerator.initQueryWrapper(wmsInspect, req.getParameterMap());
		Page<WmsInspect> page = new Page<WmsInspect>(pageNo, pageSize);
		IPage<WmsInspect> pageList = wmsInspectService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param wmsInspect
	 * @return
	 */
	@AutoLog(value = "检验记录-添加")
	@ApiOperation(value="检验记录-添加", notes="检验记录-添加")
	@RequiresPermissions("admin:wms_inspect:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WmsInspect wmsInspect) {
		wmsInspectService.save(wmsInspect);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param wmsInspect
	 * @return
	 */
	@AutoLog(value = "检验记录-编辑")
	@ApiOperation(value="检验记录-编辑", notes="检验记录-编辑")
	@RequiresPermissions("admin:wms_inspect:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WmsInspect wmsInspect) {
		wmsInspectService.updateById(wmsInspect);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "检验记录-通过id删除")
	@ApiOperation(value="检验记录-通过id删除", notes="检验记录-通过id删除")
	@RequiresPermissions("admin:wms_inspect:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		wmsInspectService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "检验记录-批量删除")
	@ApiOperation(value="检验记录-批量删除", notes="检验记录-批量删除")
	@RequiresPermissions("admin:wms_inspect:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.wmsInspectService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "检验记录-通过id查询")
	@ApiOperation(value="检验记录-通过id查询", notes="检验记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WmsInspect> queryById(@RequestParam(name="id",required=true) String id) {
		WmsInspect wmsInspect = wmsInspectService.getById(id);
		if(wmsInspect==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(wmsInspect);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param wmsInspect
    */
    @RequiresPermissions("admin:wms_inspect:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WmsInspect wmsInspect) {
        return super.exportXls(request, wmsInspect, WmsInspect.class, "检验记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:wms_inspect:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WmsInspect.class);
    }

}
