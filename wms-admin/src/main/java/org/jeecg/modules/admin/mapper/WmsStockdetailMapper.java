package org.jeecg.modules.admin.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.admin.entity.WmsInventoryCountPlan;
import org.jeecg.modules.admin.entity.WmsStockdetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 库存表
 * @Author: jeecg-boot
 * @Date:   2024-06-17
 * @Version: V1.0
 */
public interface WmsStockdetailMapper extends BaseMapper<WmsStockdetail> {

    IPage<WmsStockdetail> listSummary(Page<WmsStockdetail> page, @Param(Constants.WRAPPER) QueryWrapper<WmsStockdetail> queryWrapper);

    List<WmsStockdetail> queryWithNullObDtlId(@Param(Constants.WRAPPER) QueryWrapper<WmsStockdetail> queryWrapper,String levelNo);

    List<WmsStockdetail> queryWithNullObDtlIdForFlat(@Param(Constants.WRAPPER) QueryWrapper<WmsStockdetail> queryWrapper);

    List<WmsStockdetail> getCheckStock(@Param("wmsInventoryCountPlan") WmsInventoryCountPlan plan);
    
    /**
     * 按照品号、品名、批号、区域、托盘条码、货位编号相同的条件汇总库存
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    IPage<WmsStockdetail> queryMaterialSummary(Page<WmsStockdetail> page, @Param(Constants.WRAPPER) QueryWrapper<WmsStockdetail> queryWrapper);
    
    /**
     * 查询库存预警信息
     * 包括库存不足和库存积压的物料
     * @return 库存预警列表
     */
    List<Map<String, Object>> queryStockAlert();
}
