package org.jeecg.modules.admin.enums;

/**
 * 盘点任务状态
 */
public enum CountTaskStatusEnum {

    // 盘点创建
    PANDIANCHUANGJIAN("0","盘点创建"),
    // 盘点完成
    PANDIANWANCHENG("1","盘点完成"),

    // 盘点同步上游完成
    PANDIANSYNCWANCHENG("2","盘点同步上游完成"),

    // 盘点同步上游失败
    PANDIANSYNCFALI("3","盘点同步上游失败"),

    // 同步WMS完成
    PANDIANWMSWANCHENG("4","同步WMS完成"),
    ;

    private String code;

    private String name;

    CountTaskStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
