package org.jeecg.modules.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.admin.entity.WmsTaskQueueAgv;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
 * @Description: AGV任务
 * @Author: jeecg-boot
 * @Date:   2024-08-20
 * @Version: V1.0
 */
public interface IWmsTaskQueueAgvService extends IService<WmsTaskQueueAgv> {

    IPage<WmsTaskQueueAgv> pageList(Page<WmsTaskQueueAgv> page, QueryWrapper<WmsTaskQueueAgv> queryWrapper);
    
    /**
     * 查询当天AGV库内备货任务完成率
     * 查询taskType为"18"的任务，计算taskState为"4"的任务占比
     * @return 包含完成率和任务统计的Map
     */
    Map<String, Object> getKNBHAgvTaskCompletionRate();
}
