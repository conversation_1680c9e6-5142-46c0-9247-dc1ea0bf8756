package org.jeecg.modules.admin.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.entity.WmsFeatureConfiguration;
import org.jeecg.modules.admin.service.IWmsFeatureConfigurationService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 功能配置
 * @Author: jeecg-boot
 * @Date:   2024-07-11
 * @Version: V1.0
 */
@Api(tags="功能配置")
@RestController
@RequestMapping("/admin/wmsFeatureConfiguration")
@Slf4j
public class WmsFeatureConfigurationController extends JeecgController<WmsFeatureConfiguration, IWmsFeatureConfigurationService> {
	@Autowired
	private IWmsFeatureConfigurationService wmsFeatureConfigurationService;


	/**
	 * 分页列表查询
	 *
	 * @param wmsFeatureConfiguration
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "功能配置-分页列表查询")
	@ApiOperation(value="功能配置-分页列表查询", notes="功能配置-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WmsFeatureConfiguration>> queryPageList(WmsFeatureConfiguration wmsFeatureConfiguration,
																@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
																@RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
																HttpServletRequest req) {
		QueryWrapper<WmsFeatureConfiguration> queryWrapper = QueryGenerator.initQueryWrapper(wmsFeatureConfiguration, req.getParameterMap());
		Page<WmsFeatureConfiguration> page = new Page<WmsFeatureConfiguration>(pageNo, pageSize);
		IPage<WmsFeatureConfiguration> pageList = wmsFeatureConfigurationService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 * 查询所有数据
	 * @param wmsFeatureConfiguration
	 */
	@ApiOperation(value="查询所有数据", notes="查询所有数据")
	@GetMapping(value = "/queryAll")
	public Result<List<WmsFeatureConfiguration>> queryAll(WmsFeatureConfiguration wmsFeatureConfiguration,HttpServletRequest req) {
		QueryWrapper<WmsFeatureConfiguration> queryWrapper = QueryGenerator.initQueryWrapper(wmsFeatureConfiguration, req.getParameterMap());
		List<WmsFeatureConfiguration> list = wmsFeatureConfigurationService.list(queryWrapper);
		return Result.OK(list);
	}

	/**
	 *   添加
	 *
	 * @param wmsFeatureConfiguration
	 * @return
	 */
	@AutoLog(value = "功能配置-添加")
	@ApiOperation(value="功能配置-添加", notes="功能配置-添加")
	@RequiresPermissions("admin:wms_feature_configuration:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WmsFeatureConfiguration wmsFeatureConfiguration) {
		wmsFeatureConfigurationService.save(wmsFeatureConfiguration);
		return Result.OK("添加成功！");
	}

	/**
	 * 添加或者更新
	 * @param wmsFeatureConfiguration
	 * @return
	 */
	@ApiOperation(value="功能配置-添加或者更新", notes="功能配置-添加或者更新")
	@PostMapping(value = "/addOrUpdate")
	public Result<String> addOrUpdate(@RequestBody WmsFeatureConfiguration wmsFeatureConfiguration) {
		//先根据field查询是否存在
		QueryWrapper<WmsFeatureConfiguration> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("field", wmsFeatureConfiguration.getField());
		WmsFeatureConfiguration one = wmsFeatureConfigurationService.getOne(queryWrapper);
		if(one==null) {
			wmsFeatureConfigurationService.save(wmsFeatureConfiguration);
		}else {
			wmsFeatureConfiguration.setId(one.getId());
			wmsFeatureConfigurationService.updateById(wmsFeatureConfiguration);
		}
		return Result.OK("添加成功！");
	}

	/**
	 *  批量添加
	 *  @param wmsFeatureConfigurationList
	 *  @return
	 */
	@AutoLog(value = "功能配置-批量添加")
	@ApiOperation(value = "功能配置-批量添加", notes = "功能配置-批量添加")
	@RequiresPermissions("admin:wms_feature_configuration:add")
	@PostMapping(value = "/addBatch")
	public Result<String> addBatch(@RequestBody List<WmsFeatureConfiguration> wmsFeatureConfigurationList) {
		wmsFeatureConfigurationService.saveMain(wmsFeatureConfigurationList);
		return Result.OK("批量添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param wmsFeatureConfiguration
	 * @return
	 */
	@AutoLog(value = "功能配置-编辑")
	@ApiOperation(value="功能配置-编辑", notes="功能配置-编辑")
	@RequiresPermissions("admin:wms_feature_configuration:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WmsFeatureConfiguration wmsFeatureConfiguration) {
		wmsFeatureConfigurationService.updateById(wmsFeatureConfiguration);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "功能配置-通过id删除")
	@ApiOperation(value="功能配置-通过id删除", notes="功能配置-通过id删除")
	@RequiresPermissions("admin:wms_feature_configuration:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		wmsFeatureConfigurationService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 * 查询看板配置
	 * @param field
	 * @return
	 */
	@ApiOperation(value="查询看板配置", notes="查询看板配置")
	@GetMapping(value = "/queryBoardByField")
	public Result<String> queryBoardByField(@RequestParam(name="field",required=true) String field) {
		QueryWrapper<WmsFeatureConfiguration> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("type", "boardConfigList");
		queryWrapper.eq("field", field);
		WmsFeatureConfiguration wmsFeatureConfiguration = wmsFeatureConfigurationService.getOne(queryWrapper);
		if(wmsFeatureConfiguration==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(wmsFeatureConfiguration.getValue());
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "功能配置-批量删除")
	@ApiOperation(value="功能配置-批量删除", notes="功能配置-批量删除")
	@RequiresPermissions("admin:wms_feature_configuration:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		System.out.println("ids: " + ids);
		this.wmsFeatureConfigurationService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "功能配置-通过id查询")
	@ApiOperation(value="功能配置-通过id查询", notes="功能配置-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WmsFeatureConfiguration> queryById(@RequestParam(name="id",required=true) String id) {
		WmsFeatureConfiguration wmsFeatureConfiguration = wmsFeatureConfigurationService.getById(id);
		if(wmsFeatureConfiguration==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(wmsFeatureConfiguration);
	}

	/**
	 * 导出excel
	 *
	 * @param request
	 * @param wmsFeatureConfiguration
	 */
	@RequiresPermissions("admin:wms_feature_configuration:exportXls")
	@RequestMapping(value = "/exportXls")
	public ModelAndView exportXls(HttpServletRequest request, WmsFeatureConfiguration wmsFeatureConfiguration) {
		return super.exportXls(request, wmsFeatureConfiguration, WmsFeatureConfiguration.class, "功能配置");
	}

	/**
	 * 通过excel导入数据
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequiresPermissions("admin:wms_feature_configuration:importExcel")
	@RequestMapping(value = "/importExcel", method = RequestMethod.POST)
	public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		return super.importExcel(request, response, WmsFeatureConfiguration.class);
	}

}
