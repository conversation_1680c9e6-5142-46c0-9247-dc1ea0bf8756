<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.admin.mapper.WmsInventoryCountTaskMapper">

    <select id="getSumQuantityTaskPlanId" resultType="org.jeecg.modules.admin.bean.res.WmsCountTaskVo">
        select
            item_code,
            item_name,
            sum(system_quantity) sumSystemQuantity,
            sum(counted_quantity) sumCountedQuantity,
            sum(difference_quantity) sumDifferenceQuantity,
            sum(confirm_quantity) sumConfirmQuantity
        from
            wms_inventory_count_task
        where
            task_status = '1'
            <if test=" ids != null and ids.size() > 0">
                and plan_id in
                <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        group by item_code, item_name
    </select>

    <select id="getSumQuantityTaskId" resultType="org.jeecg.modules.admin.bean.res.WmsCountTaskVo">
        select
            item_code,
            item_name,
            sum(system_quantity) sumSystemQuantity,
            sum(counted_quantity) sumCountedQuantity,
            sum(difference_quantity) sumDifferenceQuantity,
            sum(confirm_quantity) sumConfirmQuantity
        from
            wms_inventory_count_task
        where
            task_status = #{taskStatus}
        <if test=" ids != null and ids.size() > 0">
            and id in
            <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        group by item_code, item_name
    </select>

</mapper>