package org.jeecg.modules.admin.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jeecg.common.api.dto.LogDTO;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.base.service.BaseCommonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.InetAddress;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * HTTP客户端工具类，封装了常用的HTTP请求方法
 */
public class HttpClientUtil {
    private static final Logger log = LoggerFactory.getLogger(HttpClientUtil.class);

    /**
     * 发送POST请求，返回JSONObject结果
     * @param url 请求URL
     * @param requestBody 请求体对象
     * @return 响应JSON对象
     */
    public static JSONObject sendPostRequest(String url, Object requestBody) {
        // 创建HttpClient实例
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 创建HttpPost请求
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json");

            // 构建请求体
            String json = JSON.toJSONString(requestBody);
            StringEntity entity = new StringEntity(json, "UTF-8");
            httpPost.setEntity(entity);

            // 执行请求
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                // 检查响应状态
                int statusCode = response.getStatusLine().getStatusCode();
                String responseBody = EntityUtils.toString(response.getEntity(), "UTF-8");
                if (statusCode >= 200 && statusCode < 300) {
                    return JSON.parseObject(responseBody);
                } else {
                    return null;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 发送POST请求，返回JSONArray结果
     * @param url 请求URL
     * @param requestBody 请求体对象
     * @return 响应JSON数组
     */
    public static JSONArray sendPostRequestForArray(String url, Object requestBody) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json");

            String json = JSON.toJSONString(requestBody);
            StringEntity entity = new StringEntity(json, "UTF-8");
            httpPost.setEntity(entity);

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                int statusCode = response.getStatusLine().getStatusCode();
                String responseBody = EntityUtils.toString(response.getEntity(), "UTF-8");
                if (statusCode >= 200 && statusCode < 300) {
                    return JSON.parseArray(responseBody);
                } else {
                    return null;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 发送GET请求，返回JSONObject结果
     * @param url 请求URL
     * @return 响应JSON对象
     */
    public static JSONObject sendGetRequest(String url) {
        // 创建HttpClient实例
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 创建HttpGet请求
            HttpGet httpGet = new HttpGet(url);
            httpGet.setHeader("Content-Type", "application/json");

            // 执行请求
            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                // 检查响应状态
                int statusCode = response.getStatusLine().getStatusCode();
                String responseBody = EntityUtils.toString(response.getEntity(), "UTF-8");
                if (statusCode >= 200 && statusCode < 300) {
                    return JSON.parseObject(responseBody);
                } else {
                    return null;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 执行POST请求
     * @param url 请求URL
     * @param token 认证token
     * @param param 请求参数
     * @param baseCommonService 日志服务
     * @return 响应字符串
     */
    public static String doPost(String url, String token, String param, BaseCommonService baseCommonService) {
        long startTime = System.currentTimeMillis();
        LoginUser sysUser = new LoginUser();
        sysUser.setUsername("system");
        sysUser.setRealname("系统用户");
        try (CloseableHttpClient httpClient = HttpClients.createDefault()){
            HttpPost post = new HttpPost(url);
            post.setHeader("Authorization", "Bearer " + token);
            post.setHeader("Content-Type", "application/json;charset=utf-8");
            post.setEntity(new StringEntity(param, StandardCharsets.UTF_8));

            try (CloseableHttpResponse response = httpClient.execute(post)) {
                long endTime = System.currentTimeMillis();
                long costTime = endTime - startTime;
                int statusCode = response.getStatusLine().getStatusCode();
                String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                if (statusCode == 200) {
                    return responseBody;
                } else {
                    // 记录日志
                    baseCommonService.addLog(new LogDTO(
                            "调用盘点回传接口失败",
                            CommonConstant.LOG_TYPE_3,
                            null,
                            sysUser,
                            param,
                            "POST",
                            url,
                            "syncProductOrderData",
                            "状态码：" + statusCode + "，响应：" + responseBody,
                            getLocalIpAddress(),
                            costTime
                    ));
                    return null;
                }
            }
        } catch (Exception e) {
            throw new JeecgBootException("接口调用失败：" + e.getMessage());
        }
    }

    /**
     * 获取本地IP地址
     */
    private static String getLocalIpAddress() {
        try {
            InetAddress localHost = InetAddress.getLocalHost();
            return localHost.getHostAddress();
        } catch (UnknownHostException e) {
            return "未知";
        }
    }

    /**
     * 增强版POST请求方法，提供全面的HTTP请求支持
     * @param url 请求URL
     * @param token 认证token
     * @param requestBody 请求体JSON对象
     * @param baseCommonService 日志服务
     * @param moduleName 模块名称（用于日志）
     * @param methodName 方法名称（用于日志）
     * @return 响应的JSON对象
     */
    public static JSONObject doPostEnhanced(String url, String token, JSONObject requestBody, 
                                         BaseCommonService baseCommonService, 
                                         String moduleName, String methodName) {
        long startTime = System.currentTimeMillis();
        LoginUser sysUser = createSystemUser();
        String requestParam = requestBody != null ? requestBody.toJSONString() : "{}";
        
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost post = new HttpPost(url);
            
            // 设置请求头
            if (token != null && !token.isEmpty()) {
                post.setHeader("Authorization", "Bearer " + token);
            }
            post.setHeader("Content-Type", "application/json;charset=utf-8");
            post.setEntity(new StringEntity(requestParam, StandardCharsets.UTF_8));

            try (CloseableHttpResponse response = httpClient.execute(post)) {
                long endTime = System.currentTimeMillis();
                long costTime = endTime - startTime;

                int statusCode = response.getStatusLine().getStatusCode();
                String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                
                if (statusCode == 200) {
                    return JSONObject.parseObject(responseBody);
                } else {
                    log.error("请求失败，状态码：{}，URL：{}，参数：{}，响应：{}", 
                            statusCode, url, requestParam, responseBody);
                    
                    // 记录日志
                    if (baseCommonService != null) {
                        baseCommonService.addLog(new LogDTO(
                                "调用" + moduleName + "接口失败",
                                CommonConstant.LOG_TYPE_3,
                                null,
                                sysUser,
                                requestParam,
                                "POST",
                                url,
                                methodName,
                                "状态码：" + statusCode + "，响应：" + responseBody,
                                getLocalIpAddress(),
                                costTime
                        ));
                    }
                    throw new RuntimeException("调用" + moduleName + "接口失败，状态码：" + statusCode);
                }
            }
        } catch (IOException e) {
            long endTime = System.currentTimeMillis();
            long costTime = endTime - startTime;
            log.error("请求IO异常，URL：{}，异常信息：{}", url, e.getMessage());
            
            // 记录日志
            if (baseCommonService != null) {
                baseCommonService.addLog(new LogDTO(
                        "调用" + moduleName + "接口IO异常",
                        CommonConstant.LOG_TYPE_3,
                        null,
                        sysUser,
                        requestParam,
                        "POST",
                        url,
                        methodName,
                        "IO异常：" + e.getMessage(),
                        getLocalIpAddress(),
                        costTime
                ));
            }
            throw new RuntimeException("调用" + moduleName + "接口时发生IO异常", e);
        }
    }

    /**
     * 增强版GET请求方法，提供全面的HTTP请求支持
     * @param url 请求URL
     * @param token 认证token
     * @param requestParams 请求参数
     * @param baseCommonService 日志服务
     * @param moduleName 模块名称（用于日志）
     * @param methodName 方法名称（用于日志）
     * @return 响应的JSON对象
     */
    public static JSONObject doGetEnhanced(String url, String token, JSONObject requestParams, 
                                        BaseCommonService baseCommonService, 
                                        String moduleName, String methodName) {
        long startTime = System.currentTimeMillis();
        LoginUser sysUser = createSystemUser();
        String requestParam = (requestParams != null) ? requestParams.toJSONString() : "{}";
        CloseableHttpResponse response = null;

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            URIBuilder uriBuilder = new URIBuilder(url);

            // 将JSON中的key-value作为QueryParam
            if (requestParams != null && !requestParams.isEmpty()) {
                for (String key : requestParams.keySet()) {
                    Object value = requestParams.get(key);
                    uriBuilder.addParameter(key, value == null ? "" : value.toString());
                }
            }
            
            URI finalUri = uriBuilder.build();
            HttpGet get = new HttpGet(finalUri);

            // 设置Header
            if (token != null && !token.isEmpty()) {
                get.setHeader("Authorization", "Bearer " + token);
            }
            get.setHeader("Content-Type", "application/json;charset=utf-8");

            response = httpClient.execute(get);

            long endTime = System.currentTimeMillis();
            long costTime = endTime - startTime;
            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

            if (statusCode == 200) {
                return JSONObject.parseObject(responseBody);
            } else {
                log.error("请求失败，状态码：{}，URL：{}，参数：{}，响应：{}", 
                        statusCode, finalUri, requestParam, responseBody);
                
                // 记录日志
                if (baseCommonService != null) {
                    baseCommonService.addLog(new LogDTO(
                            "调用" + moduleName + "接口失败",
                            CommonConstant.LOG_TYPE_3,
                            null,
                            sysUser,
                            requestParam,
                            "GET",
                            finalUri.toString(),
                            methodName,
                            "状态码：" + statusCode + "，响应：" + responseBody,
                            getLocalIpAddress(),
                            costTime
                    ));
                }
                throw new RuntimeException("调用" + moduleName + "接口失败，状态码：" + statusCode);
            }
        } catch (IOException | URISyntaxException e) {
            long endTime = System.currentTimeMillis();
            long costTime = endTime - startTime;
            log.error("请求IO/URI异常，URL：{}，异常信息：{}", url, e.getMessage());
            
            // 记录日志
            if (baseCommonService != null) {
                baseCommonService.addLog(new LogDTO(
                        "调用" + moduleName + "接口IO/URI异常",
                        CommonConstant.LOG_TYPE_3,
                        null,
                        sysUser,
                        requestParam,
                        "GET",
                        url,
                        methodName,
                        "IO/URI异常：" + e.getMessage(),
                        getLocalIpAddress(),
                        costTime
                ));
            }
            throw new RuntimeException("调用" + moduleName + "接口时发生IO/URI异常: " + e.getMessage(), e);
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException ignore) {
                }
            }
        }
    }

    /**
     * 创建系统用户对象
     */
    private static LoginUser createSystemUser() {
        LoginUser sysUser = new LoginUser();
        sysUser.setUsername("system");
        sysUser.setRealname("系统用户");
        return sysUser;
    }
}
