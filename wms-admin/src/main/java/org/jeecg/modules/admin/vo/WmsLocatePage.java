package org.jeecg.modules.admin.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 货位表
 * @Author: jeecg-boot
 * @Date:   2024-06-15
 * @Version: V1.0
 */
@Data
@ApiModel(value="wms_locate_page对象", description="货位表")
public class WmsLocatePage implements Serializable {

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
	/**货位编号*/
	@Excel(name = "货位编号", width = 15)
    @ApiModelProperty(value = "货位编号")
    private String locateCode;
    /**货位名称*/
    @Excel(name = "货位名称", width = 15)
    @ApiModelProperty(value = "货位名称")
    private String locateName;
    /**容器条码*/
    @Excel(name = "容器条码", width = 15)
    @ApiModelProperty(value = "容器条码")
    private String lpn;
    /**排*/
    @Excel(name = "排", width = 15)
    @ApiModelProperty(value = "排")
    private java.lang.Integer rowNo;
    /**列*/
    @Excel(name = "列", width = 15)
    @ApiModelProperty(value = "列")
    private java.lang.Integer colNo;
    /**层*/
    @Excel(name = "层", width = 15)
    @ApiModelProperty(value = "层")
    private java.lang.Integer levelNo;
    /**品号*/
    @Excel(name = "品号", width = 15)
    @ApiModelProperty(value = "品号")
    private String itemCode;
    /**品名*/
    @Excel(name = "品名", width = 15)
    @ApiModelProperty(value = "品名")
    private String itemName;
    /**规格*/
    @Excel(name = "规格", width = 15)
    @ApiModelProperty(value = "规格")
    private String itemSpec;
    /**批次号*/
    @Excel(name = "批次号", width = 15)
    @ApiModelProperty(value = "批次号")
    private String batchCode;
    /**物料条码*/
    @Excel(name = "物料条码", width = 15)
    @ApiModelProperty(value = "物料条码")
    private String itemBarcode;
    /**数量*/
    @Excel(name = "数量", width = 15)
    @ApiModelProperty(value = "数量")
    private Integer quantity;
    /**库存状态*/
    @Excel(name = "库存状态", width = 15, dicCode = "inv_state")
    @Dict(dicCode = "inv_state")
    @ApiModelProperty(value = "库存状态")
    private String invState;
    /**生产日期*/
    @Excel(name = "生产日期", width = 15)
    @ApiModelProperty(value = "生产日期")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date productDate;
	/**入库时间*/
    @Excel(name = "入库时间", width = 15)
    @ApiModelProperty(value = "入库时间")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date inboundTime;
    /**库存天数*/
    @Excel(name = "库存天数", width = 15)
    @ApiModelProperty(value = "库存天数")
    private Integer stockAge;
    /**ERP状态*/
    @Excel(name = "ERP状态", width = 15, dicCode = "erp_posting")
    @Dict(dicCode = "erp_posting")
    @ApiModelProperty(value = "ERP状态")
    private java.lang.String erpPosting;
	/**货位标识*/
	@Excel(name = "货位标识", width = 15, dicCode = "locate_operate_state")
	@Dict(dicCode = "locate_operate_state")
    @ApiModelProperty(value = "货位标识")
    private String locateOperateState;
    /**货位类型*/
    @Excel(name = "货位类型", width = 15, dicCode = "locate_type")
    @Dict(dicCode = "locate_type")
    @ApiModelProperty(value = "货位类型")
    private java.lang.String locateType;
    /**wcs货位类型*/
    @Excel(name = "wcs货位类型", width = 15, dicCode = "wcs_locate_type")
    @ApiModelProperty(value = "wcs货位类型")
    @Dict(dicCode = "wcs_locate_type")
    private java.lang.String wcsLocateType;
    /**货位状态*/
    @Excel(name = "货位状态", width = 15, dicCode = "locate_state")
    @Dict(dicCode = "locate_state")
    @ApiModelProperty(value = "货位状态")
    private java.lang.String locateState;
    /**物料类型*/
    @Excel(name = "物料类型", width = 15)
    @ApiModelProperty(value = "物料类型")
    @Dict(dicCode = "materiel_type")
    private java.lang.String materielType;
}
