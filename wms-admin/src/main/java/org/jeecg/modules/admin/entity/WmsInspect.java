package org.jeecg.modules.admin.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 检验记录
 * @Author: jeecg-boot
 * @Date:   2025-02-20
 * @Version: V1.0
 */
@Data
@TableName("wms_inspect")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="wms_inspect对象", description="检验记录")
public class WmsInspect implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**单据标识*/
	@Excel(name = "单据标识", width = 15)
    @ApiModelProperty(value = "单据标识")
    private java.lang.Integer serialNo;
	/**检验单号*/
	@Excel(name = "检验单号", width = 15)
    @ApiModelProperty(value = "检验单号")
    private java.lang.String workNo;
	/**收料单据号*/
	@Excel(name = "收料单据号", width = 15)
    @ApiModelProperty(value = "收料单据号")
    private java.lang.String billNo;
	/**单据状态*/
	@Excel(name = "单据状态", width = 15, dicCode = "wms_inspect_bill_status")
	@Dict(dicCode = "wms_inspect_bill_status")
    @ApiModelProperty(value = "单据状态")
    private java.lang.String billStatus;
	/**品号*/
	@Excel(name = "品号", width = 15)
    @ApiModelProperty(value = "品号")
    private java.lang.String itemCode;
	/**品名*/
	@Excel(name = "品名", width = 15)
    @ApiModelProperty(value = "品名")
    private java.lang.String itemName;
	/**规格*/
	@Excel(name = "规格", width = 15)
    @ApiModelProperty(value = "规格")
    private java.lang.String itemSpec;
	/**批号*/
	@Excel(name = "批号", width = 15)
    @ApiModelProperty(value = "批号")
    private java.lang.String batchCode;
	/**合格数量*/
	@Excel(name = "合格数量", width = 15)
    @ApiModelProperty(value = "合格数量")
    private java.lang.Double standardQty;
	/**不合格数量*/
	@Excel(name = "不合格数量", width = 15)
    @ApiModelProperty(value = "不合格数量")
    private java.lang.Double unstandardQty;
	/**检验结果*/
	@Excel(name = "检验结果", width = 15, dicCode = "check_tag")
	@Dict(dicCode = "check_tag")
    @ApiModelProperty(value = "检验结果")
    private java.lang.String inspectResult;
	/**处理结果*/
	@Excel(name = "处理结果", width = 15, dicCode = "inspect_measures")
	@Dict(dicCode = "inspect_measures")
    @ApiModelProperty(value = "处理结果")
    private java.lang.String processResult;
	/**检验日期*/
	@Excel(name = "检验日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "检验日期")
    private java.util.Date inspectDate;
	/**单据备注*/
	@Excel(name = "单据备注", width = 15)
    @ApiModelProperty(value = "单据备注")
    private java.lang.String remark;
}
