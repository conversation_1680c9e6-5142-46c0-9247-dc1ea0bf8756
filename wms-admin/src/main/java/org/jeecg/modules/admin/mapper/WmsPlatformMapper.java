package org.jeecg.modules.admin.mapper;

import java.util.List;

import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.admin.entity.WmsPlatform;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 站台表
 * @Author: jeecg-boot
 * @Date:   2024-06-22
 * @Version: V1.0
 */
public interface WmsPlatformMapper extends BaseMapper<WmsPlatform> {

    String getPickInfo(String platformCode);
}
