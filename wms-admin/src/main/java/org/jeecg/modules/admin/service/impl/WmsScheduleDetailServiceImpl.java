package org.jeecg.modules.admin.service.impl;

import org.jeecg.modules.admin.entity.WmsScheduleDetail;
import org.jeecg.modules.admin.mapper.WmsScheduleDetailMapper;
import org.jeecg.modules.admin.service.IWmsScheduleDetailService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 排运单明细
 * @Author: jeecg-boot
 * @Date:   2024-10-26
 * @Version: V1.0
 */
@Service
public class WmsScheduleDetailServiceImpl extends ServiceImpl<WmsScheduleDetailMapper, WmsScheduleDetail> implements IWmsScheduleDetailService {
	
	@Autowired
	private WmsScheduleDetailMapper wmsScheduleDetailMapper;
	
	@Override
	public List<WmsScheduleDetail> selectByMainId(String mainId) {
		return wmsScheduleDetailMapper.selectByMainId(mainId);
	}
}
