package org.jeecg.modules.admin.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecg.modules.admin.entity.WmsSenddetail;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @Description: 拣选
 * @Author: jeecg-boot
 * @Date:   2024-06-19
 * @Version: V1.0
 */
@Data
@ApiModel(value="wms_sendPage对象", description="拣选")
public class OutSelectPage {

	/**明细id*/
	@ApiModelProperty(value = "明细id")
    private String detailID;
	/**整托出库口*/
	@ApiModelProperty(value = "整托出库口")
    private String outstation;
	/**单据类型*/
	private String bill_type;
	/**库存*/
	private List<StockPage> proStockList;
}
