package org.jeecg.modules.admin.service;

import com.alibaba.fastjson.JSONArray;
import org.jeecg.modules.admin.entity.WmsScheduleDetail;
import org.jeecg.modules.admin.entity.WmsSchedule;
import com.baomidou.mybatisplus.extension.service.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 排运单
 * @Author: jeecg-boot
 * @Date:   2024-10-26
 * @Version: V1.0
 */
public interface IWmsScheduleService extends IService<WmsSchedule> {

	/**
	 * 添加一对多
	 *
	 * @param wmsSchedule
	 * @param wmsScheduleDetailList
	 */
	public void saveMain(WmsSchedule wmsSchedule,List<WmsScheduleDetail> wmsScheduleDetailList) ;
	
	/**
	 * 修改一对多
	 *
	 * @param wmsSchedule
	 * @param wmsScheduleDetailList
	 */
	public void updateMain(WmsSchedule wmsSchedule,List<WmsScheduleDetail> wmsScheduleDetailList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);
}
