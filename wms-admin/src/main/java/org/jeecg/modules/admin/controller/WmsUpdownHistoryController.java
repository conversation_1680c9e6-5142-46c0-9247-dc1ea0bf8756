package org.jeecg.modules.admin.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.entity.WmsUpdownHistory;
import org.jeecg.modules.admin.service.IWmsUpdownHistoryService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 上下架历史
 * @Author: jeecg-boot
 * @Date:   2025-04-08
 * @Version: V1.0
 */
@Api(tags="上下架历史")
@RestController
@RequestMapping("/admin/wmsUpdownHistory")
@Slf4j
public class WmsUpdownHistoryController extends JeecgController<WmsUpdownHistory, IWmsUpdownHistoryService> {
	@Autowired
	private IWmsUpdownHistoryService wmsUpdownHistoryService;
	
	/**
	 * 分页列表查询
	 *
	 * @param wmsUpdownHistory
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "上下架历史-分页列表查询")
	@ApiOperation(value="上下架历史-分页列表查询", notes="上下架历史-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WmsUpdownHistory>> queryPageList(WmsUpdownHistory wmsUpdownHistory,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WmsUpdownHistory> queryWrapper = QueryGenerator.initQueryWrapper(wmsUpdownHistory, req.getParameterMap());
		Page<WmsUpdownHistory> page = new Page<WmsUpdownHistory>(pageNo, pageSize);
		IPage<WmsUpdownHistory> pageList = wmsUpdownHistoryService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param wmsUpdownHistory
	 * @return
	 */
	@AutoLog(value = "上下架历史-添加")
	@ApiOperation(value="上下架历史-添加", notes="上下架历史-添加")
	@RequiresPermissions("admin:wms_updown_history:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WmsUpdownHistory wmsUpdownHistory) {
		wmsUpdownHistoryService.save(wmsUpdownHistory);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param wmsUpdownHistory
	 * @return
	 */
	@AutoLog(value = "上下架历史-编辑")
	@ApiOperation(value="上下架历史-编辑", notes="上下架历史-编辑")
	@RequiresPermissions("admin:wms_updown_history:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WmsUpdownHistory wmsUpdownHistory) {
		wmsUpdownHistoryService.updateById(wmsUpdownHistory);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "上下架历史-通过id删除")
	@ApiOperation(value="上下架历史-通过id删除", notes="上下架历史-通过id删除")
	@RequiresPermissions("admin:wms_updown_history:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		wmsUpdownHistoryService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "上下架历史-批量删除")
	@ApiOperation(value="上下架历史-批量删除", notes="上下架历史-批量删除")
	@RequiresPermissions("admin:wms_updown_history:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.wmsUpdownHistoryService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "上下架历史-通过id查询")
	@ApiOperation(value="上下架历史-通过id查询", notes="上下架历史-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WmsUpdownHistory> queryById(@RequestParam(name="id",required=true) String id) {
		WmsUpdownHistory wmsUpdownHistory = wmsUpdownHistoryService.getById(id);
		if(wmsUpdownHistory==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(wmsUpdownHistory);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param wmsUpdownHistory
    */
    @RequiresPermissions("admin:wms_updown_history:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WmsUpdownHistory wmsUpdownHistory) {
        return super.exportXls(request, wmsUpdownHistory, WmsUpdownHistory.class, "上下架历史");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:wms_updown_history:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WmsUpdownHistory.class);
    }

}
