package org.jeecg.modules.admin.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.entity.WmsCountPlanWarehouse;
import org.jeecg.modules.admin.service.IWmsCountPlanWarehouseService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 盘点方案与仓库关联表
 * @Author: jeecg-boot
 * @Date:   2024-12-19
 * @Version: V1.0
 */
@Api(tags="盘点方案与仓库关联表")
@RestController
@RequestMapping("/admin/wmsCountPlanWarehouse")
@Slf4j
public class WmsCountPlanWarehouseController extends JeecgController<WmsCountPlanWarehouse, IWmsCountPlanWarehouseService> {
	@Autowired
	private IWmsCountPlanWarehouseService wmsCountPlanWarehouseService;
	
	/**
	 * 分页列表查询
	 *
	 * @param wmsCountPlanWarehouse
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "盘点方案与仓库关联表-分页列表查询")
	@ApiOperation(value="盘点方案与仓库关联表-分页列表查询", notes="盘点方案与仓库关联表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WmsCountPlanWarehouse>> queryPageList(WmsCountPlanWarehouse wmsCountPlanWarehouse,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WmsCountPlanWarehouse> queryWrapper = QueryGenerator.initQueryWrapper(wmsCountPlanWarehouse, req.getParameterMap());
		Page<WmsCountPlanWarehouse> page = new Page<WmsCountPlanWarehouse>(pageNo, pageSize);
		IPage<WmsCountPlanWarehouse> pageList = wmsCountPlanWarehouseService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param wmsCountPlanWarehouse
	 * @return
	 */
	@AutoLog(value = "盘点方案与仓库关联表-添加")
	@ApiOperation(value="盘点方案与仓库关联表-添加", notes="盘点方案与仓库关联表-添加")
	@RequiresPermissions("admin:wms_count_plan_warehouse:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WmsCountPlanWarehouse wmsCountPlanWarehouse) {
		wmsCountPlanWarehouseService.save(wmsCountPlanWarehouse);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param wmsCountPlanWarehouse
	 * @return
	 */
	@AutoLog(value = "盘点方案与仓库关联表-编辑")
	@ApiOperation(value="盘点方案与仓库关联表-编辑", notes="盘点方案与仓库关联表-编辑")
	@RequiresPermissions("admin:wms_count_plan_warehouse:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WmsCountPlanWarehouse wmsCountPlanWarehouse) {
		wmsCountPlanWarehouseService.updateById(wmsCountPlanWarehouse);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "盘点方案与仓库关联表-通过id删除")
	@ApiOperation(value="盘点方案与仓库关联表-通过id删除", notes="盘点方案与仓库关联表-通过id删除")
	@RequiresPermissions("admin:wms_count_plan_warehouse:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		wmsCountPlanWarehouseService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "盘点方案与仓库关联表-批量删除")
	@ApiOperation(value="盘点方案与仓库关联表-批量删除", notes="盘点方案与仓库关联表-批量删除")
	@RequiresPermissions("admin:wms_count_plan_warehouse:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.wmsCountPlanWarehouseService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "盘点方案与仓库关联表-通过id查询")
	@ApiOperation(value="盘点方案与仓库关联表-通过id查询", notes="盘点方案与仓库关联表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WmsCountPlanWarehouse> queryById(@RequestParam(name="id",required=true) String id) {
		WmsCountPlanWarehouse wmsCountPlanWarehouse = wmsCountPlanWarehouseService.getById(id);
		if(wmsCountPlanWarehouse==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(wmsCountPlanWarehouse);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param wmsCountPlanWarehouse
    */
    @RequiresPermissions("admin:wms_count_plan_warehouse:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WmsCountPlanWarehouse wmsCountPlanWarehouse) {
        return super.exportXls(request, wmsCountPlanWarehouse, WmsCountPlanWarehouse.class, "盘点方案与仓库关联表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:wms_count_plan_warehouse:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WmsCountPlanWarehouse.class);
    }

}
