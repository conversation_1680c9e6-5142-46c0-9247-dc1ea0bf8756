package org.jeecg.modules.admin.service;

import org.jeecg.modules.admin.entity.MesInterface;
import com.baomidou.mybatisplus.extension.service.IService;

import java.io.IOException;

/**
 * @Description: mes接口管理
 * @Author: jeecg-boot
 * @Date:   2024-12-12
 * @Version: V1.0
 */
public interface IMesInterfaceService extends IService<MesInterface> {
    /**
     * 执行MES登录接口并将access_token存储到Redis
     * @throws IOException 如果HTTP请求或Redis操作失败
     */
    void performLoginAndStoreToken() throws IOException;
    
    /**
     * 一键修改所有接口前缀
     * @param prefix 新的接口前缀
     */
    void updatePrefix(String prefix);
}
