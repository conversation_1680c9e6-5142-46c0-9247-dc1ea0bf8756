package org.jeecg.modules.admin.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.modules.admin.constant.WmsConstant;
import org.jeecg.modules.admin.entity.WmsConttask;
import org.jeecg.modules.admin.entity.WmsPlatform;
import org.jeecg.modules.admin.entity.WmsWarehouse;
import org.jeecg.modules.admin.mapper.WmsConttaskMapper;
import org.jeecg.modules.admin.mapper.WmsPlatformMapper;
import org.jeecg.modules.admin.mapper.WmsWarehouseMapper;
import org.jeecg.modules.admin.service.IWmsPlatformService;
import org.jeecg.modules.admin.util.HttpClientUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 站台表
 * @Author: jeecg-boot
 * @Date:   2024-06-22
 * @Version: V1.0
 */
@Service
public class WmsPlatformServiceImpl extends ServiceImpl<WmsPlatformMapper, WmsPlatform> implements IWmsPlatformService {

    @Autowired
    private WmsConttaskMapper wmsConttaskMapper;
    @Autowired
    private WmsWarehouseMapper wmsWarehouseMapper;
    @Autowired
    private WmsPlatformMapper wmsPlatformMapper;
    @Override
    public JSONObject updateModeById(WmsPlatform wmsPlatform) {
        JSONObject result = new JSONObject();
        // 1. 根据ID获取之前的数据
        WmsPlatform previousPlatform = super.getById(wmsPlatform.getId());
        if (previousPlatform == null) {
            result.put("result", false);
            result.put("message", "未找到对应的WmsPlatform记录");
            throw new RuntimeException("未找到对应的WmsPlatform记录，ID: " + wmsPlatform.getId());
        }

        //判断当前wmsPlatform站台类型是不是出库，拆零，入库站台，如果不是，则直接修改
        if (!wmsPlatform.getPlateType().equals(WmsConstant.PlateTypeEnum.WHOLE.getValue()) && !wmsPlatform.getPlateType().equals(WmsConstant.PlateTypeEnum.SPLIT.getValue()) && !wmsPlatform.getPlateType().equals(WmsConstant.PlateTypeEnum.IN.getValue())) {
            super.updateById(wmsPlatform);
            result.put("result", true);
            result.put("message", "编辑成功");
            return result;
        }

        // 2. 判断之前的plateType和当前的plateType是否一致
        if (previousPlatform.getPlateMode().equals(wmsPlatform.getPlateMode())) {
            // 3. 如果一致，则直接调用updateById方法
            super.updateById(wmsPlatform);
            result.put("result", true);
            result.put("message", "编辑成功");
            return result;
        }

        // 4. 如果不一致，先获取当前站台是否有未完成的任务
        QueryWrapper<WmsConttask> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("task_state", Arrays.asList("0", "1", "2"))
                .and(wrapper -> wrapper.eq("source_pos", wmsPlatform.getPlateCode())
                        .or()
                        .eq("target_pos", wmsPlatform.getPlateCode()));

        List<WmsConttask> wmsConttasks = wmsConttaskMapper.selectList(queryWrapper);

        if (!wmsConttasks.isEmpty()) {
            result.put("result", false);
            result.put("message", "当前站台存在未完成的任务，无法更新");
            // 存在未完成的任务，不能更新plateType，抛出异常
            throw new RuntimeException("当前站台存在未完成的任务，无法更新plateType");
        }

        // 获取仓库信息
        WmsWarehouse wmsWarehouse = wmsWarehouseMapper.selectById(wmsPlatform.getWarehouseId());
        if (wmsWarehouse == null) {
            throw new RuntimeException("未找到对应的WmsWarehouse记录，ID: " + wmsPlatform.getWarehouseId());
        }

        // 创建请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("warehouse", wmsWarehouse.getWarehouseCode());
        params.put("portCode", wmsPlatform.getPlateCode());
        params.put("portModel", wmsPlatform.getPlateType());

        // 发送请求到WCS
        JSONObject response = HttpClientUtil.sendPostRequest("http://60.188.20.218:20001/fromWms/portModel", params);

        if (response == null) {
            result.put("result", false);
            result.put("message", "Request failed");
        } else if (response.getInteger("returnStatus") != null) {
            if (response.getInteger("returnStatus") == 0) {
                result.put("result", true);
                result.put("message", "编辑成功");
                // 调用updateById方法
                super.updateById(wmsPlatform);
            } else {
                result.put("result", false);
                result.put("message", "编辑失败，原因:" + response.getString("returnInfo"));
            }
        } else {
            result.put("result", false);
            result.put("message", "编辑失败，原因:未知错误");
        }
        return result;
    }

    @Override
    public String getPickInfo(String platformCode) {
       return wmsPlatformMapper.getPickInfo(platformCode);
    }
}
