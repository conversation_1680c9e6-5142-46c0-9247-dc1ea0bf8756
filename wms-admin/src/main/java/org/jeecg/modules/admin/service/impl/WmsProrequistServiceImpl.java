package org.jeecg.modules.admin.service.impl;

import com.alibaba.fastjson.JSONArray;
import java.net.UnknownHostException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xkcoding.http.util.StringUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jeecg.common.api.dto.LogDTO;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.constant.FillRuleConstant;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.FillRuleUtil;
import org.jeecg.modules.admin.constant.WmsConstant;
import org.jeecg.modules.admin.entity.MesInterface;
import org.jeecg.modules.admin.entity.WmsProrequist;
import org.jeecg.modules.admin.entity.WmsProrequistDetail;
import org.jeecg.modules.admin.mapper.WmsProrequistDetailMapper;
import org.jeecg.modules.admin.mapper.WmsProrequistMapper;
import org.jeecg.modules.admin.service.IMesInterfaceService;
import org.jeecg.modules.admin.service.IWmsProrequistService;
import org.jeecg.modules.base.service.BaseCommonService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.Serializable;
import java.net.InetAddress;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Collection;
import java.util.stream.Collectors;
import org.apache.shiro.SecurityUtils;
import org.jeecg.modules.admin.util.HttpClientUtil;

/**
 * @Description: 生产单据
 * @Author: jeecg-boot
 * @Date:   2024-11-06
 * @Version: V1.0
 */
@Log4j2
@Service
public class WmsProrequistServiceImpl extends ServiceImpl<WmsProrequistMapper, WmsProrequist> implements IWmsProrequistService {

	@Autowired
	private WmsProrequistMapper wmsProrequistMapper;
	@Autowired
	private WmsProrequistDetailMapper wmsProrequistDetailMapper;
	// 注入MES接口相关服务（根据实际情况调整类型名称）
	@Autowired
	private IMesInterfaceService mesInterfaceService;
	// 注入Redis操作模板
	@Autowired
	private RedisTemplate<String, String> redisTemplate;
	// 注入日志记录服务
	@Autowired
	private BaseCommonService baseCommonService;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(WmsProrequist wmsProrequist, List<WmsProrequistDetail> wmsProrequistDetailList) {
		// 生成单据号
		String code = (String) FillRuleUtil.executeRule(FillRuleConstant.PROREQUIST_ORDER, new JSONObject());
		wmsProrequist.setBillNo(code);

		// 设置默认值：单据类型
		if (StringUtil.isEmpty(wmsProrequist.getBillType())) {
			wmsProrequist.setBillType("SCLL");
		}

		// 设置默认值：单据名称（几月几日从MES推送）
		if (StringUtil.isEmpty(wmsProrequist.getBillName())) {
			SimpleDateFormat sdf = new SimpleDateFormat("MM月dd日");
			String formattedDate = sdf.format(new Date());
			wmsProrequist.setBillName(formattedDate + "从MES推送");
		}

		// 设置默认值：单据状态
		if (StringUtil.isEmpty(wmsProrequist.getBillStatus())) {
			wmsProrequist.setBillStatus("1");
		}

		// 设置默认值：是否来自ERP
		if (StringUtil.isEmpty(wmsProrequist.getFromErp())) {
			wmsProrequist.setFromErp("1");
		}

		// 设置默认值：ERP同步状态
		if (StringUtil.isEmpty(wmsProrequist.getErpSync())) {
			wmsProrequist.setErpSync("0");
		}

		// 设置默认值：日期（当前日期）
		if (wmsProrequist.getDeliveryDate() == null) {
			wmsProrequist.setDeliveryDate(new Date());
		}

		// 保存主表数据
		wmsProrequistMapper.insert(wmsProrequist);

		// 保存明细数据
		if (wmsProrequistDetailList != null && wmsProrequistDetailList.size() > 0) {
			for (WmsProrequistDetail entity : wmsProrequistDetailList) {
				entity.setBillNo(code);
				// 外键设置
				entity.setBillId(wmsProrequist.getId());
				entity.setDisQty(0.0);
				entity.setActQty(0.0);
				entity.setLineState("1");
				wmsProrequistDetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(WmsProrequist wmsProrequist, List<WmsProrequistDetail> wmsProrequistDetailList) {
		wmsProrequistMapper.updateById(wmsProrequist);

		//1.先删除子表数据
		wmsProrequistDetailMapper.deleteByMainId(wmsProrequist.getId());

		//2.子表数据重新插入
		if (wmsProrequistDetailList != null && wmsProrequistDetailList.size() > 0) {
			for (WmsProrequistDetail entity : wmsProrequistDetailList) {
				//外键设置
				entity.setBillId(wmsProrequist.getId());
				wmsProrequistDetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		wmsProrequistDetailMapper.deleteByMainId(id);
		wmsProrequistMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for (Serializable id : idList) {
			wmsProrequistDetailMapper.deleteByMainId(id.toString());
			wmsProrequistMapper.deleteById(id);
		}
	}

	/**
	 * 生产单据提交方法：将生产单据同步到MES系统
	 *
	 * @param id   生产单据ID
	 * @param flag 提交时的强制标记，默认为false；如接口返回提示库存不足，可传true进行强制提交
	 * @return 同步结果的JSON对象，包含同步成功或失败及相应提示信息
	 */
	@Override
	public JSONObject submitRecord(String id, boolean flag) {
		// 校验生产单据ID不能为空
		if (StringUtils.isBlank(id)) {
			throw new RuntimeException("生产单据id不能为空");
		}

		// 根据ID获取生产单据主表信息
		WmsProrequist wmsProrequist = wmsProrequistMapper.selectById(id);
		if (wmsProrequist == null) {
			throw new RuntimeException("未查询到生产单据信息");
		}

		// 判断该生产单据是否已经同步ERP
		// TODO: 请根据实际字段调整判断逻辑（假设字段erpSync标识同步状态，"SYNC_SUCCESS"表示同步成功）
		if (wmsProrequist.getErpSync() != null && WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue().equals(wmsProrequist.getErpSync())) {
			throw new RuntimeException("该生产单据已同步ERP，无法再次提交");
		}

		// 根据生产单据ID获取明细信息
		List<WmsProrequistDetail> detailList = wmsProrequistDetailMapper.selectByMainId(id);
		if (detailList == null || detailList.isEmpty()) {
			throw new RuntimeException("未查询到生产单据明细信息");
		}

		// TODO: 根据实际业务判断是否需要对明细数据进行过滤或校验
		// 筛选出部分发运和全部发运的明细数据
		 List<WmsProrequistDetail> partSendList = detailList.stream()
				 .filter(detail ->  "3".equals(detail.getLineState()) || "4".equals(detail.getLineState()))
				 .collect(Collectors.toList());
		if (partSendList.isEmpty()) {
			throw new RuntimeException("未查询到部分发运或全部发运的明细数据");
		}
		//将actQty不为0的出库单明细筛出来
		partSendList = partSendList.stream().filter(wmsProrequistDetail -> wmsProrequistDetail.getActQty() != 0).collect(Collectors.toList());
		if (partSendList.isEmpty()) {
			throw new RuntimeException("未查询到实发数量不为0的生产单据明细信息");
		}
		// 获取MES接口配置信息
		// TODO: 根据实际业务替换接口编码，此处假定生产单据同步接口编码为"JK028"
		MesInterface mi = mesInterfaceService.getOne(new QueryWrapper<MesInterface>().eq("interface_code", "JK028"));
		if (mi == null) {
			throw new RuntimeException("未找到MES接口配置信息: JK028");
		}

		// 检查接口状态和请求方法
		if (!"1".equals(mi.getInterfaceStatus())) {
			throw new RuntimeException("接口JK028未启用！");
		}
		if (!"POST".equalsIgnoreCase(mi.getRequestMethod())) {
			throw new RuntimeException("接口JK028的请求方式不是POST！");
		}

		// 获取MES登录Token
		String accessToken = redisTemplate.opsForValue().get("mes_login_access_token");
		if (accessToken == null) {
			// 如果没有获取到MES登录token，则调用performLoginAndStoreToken方法获取
            try {
                log.info("未获取到MES登录token，正在调用登录接口获取...");
                mesInterfaceService.performLoginAndStoreToken();
                // 重新获取token
                accessToken = redisTemplate.opsForValue().get("mes_login_access_token");
                if (accessToken == null) {
                    throw new RuntimeException("登录接口调用成功但仍未获取到MES登录token！");
                }
                log.info("已成功获取MES登录token");
            } catch (IOException e) {
                throw new RuntimeException("调用MES登录接口时发生异常: " + e.getMessage(), e);
            }
		}

		// 构建请求参数
		JSONObject response = buildProductionRequestParam(wmsProrequist, partSendList, flag);

		// 调用MES接口
		JSONObject responseJson = doPost(mi.getInterfaceUrl(), accessToken, response);

		// 处理MES接口响应并更新生产单据状态
		return handleProductionResponse(responseJson, wmsProrequist, id);
	}

	/**
	 * 构建生产单据同步请求参数
	 *
	 * @param prorequist 生产单据主表
	 * @param detailList 生产单据明细列表
	 * @param flag       是否忽略交互标识（如库存不足等情况时的强制提交标记）
	 * @return 请求参数JSON对象
	 */
	private JSONObject buildProductionRequestParam(WmsProrequist prorequist, List<WmsProrequistDetail> detailList, boolean flag) {
		//获取当前登录用户
		LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		JSONObject requestParam = new JSONObject();
		requestParam.put("ignoreInterationFlag", flag);
		// 假定生产单据单号字段为billNo，按实际情况调整
		requestParam.put("billNo", detailList.get(0).getWorkNo());
		requestParam.put("creator", loginUser.getUsername());
		// 假定生产单据类型字段为billType，按实际情况调整
		if (StringUtils.isBlank(prorequist.getBillType())) {
			requestParam.put("type", 1);
		} else {
			if (prorequist.getBillType().equals(WmsConstant.ProrequistBillTypeEnum.SCLL.getValue())) {
				requestParam.put("type", 1);
			} else if (prorequist.getBillType().equals(WmsConstant.ProrequistBillTypeEnum.SCBL.getValue())) {
				requestParam.put("type", 3);
			}
		}

		// 遍历明细数据
		JSONArray jsonArray = new JSONArray();
		for (WmsProrequistDetail detail : detailList) {
			JSONObject detailJson = new JSONObject();
			// TODO: 根据实际MES接口要求调整字段映射，此处暂借用frealqty、fsid、fdeststockidnumber等字段名
			detailJson.put("frealqty", detail.getActQty());
			detailJson.put("fsid", detail.getFentryid());
			jsonArray.add(detailJson);
		}
		requestParam.put("entityList", jsonArray);
		log.info("生产单据同步请求参数: {}", requestParam.toJSONString());
		return requestParam;
	}

	/**
	 * 通用的HTTP POST调用方法，用于调用MES接口
	 *
	 * @param url         接口URL
	 * @param token       MES登录Token
	 * @param requestBody 请求参数
	 * @return MES接口响应的JSON对象
	 */
	private JSONObject doPost(String url, String token, JSONObject requestBody) {
		return HttpClientUtil.doPostEnhanced(url, token, requestBody, baseCommonService, "MES生产请领", "syncProrequistData");
	}

	/**
	 * 获取本机IP地址
	 *
	 * @return IP地址字符串，如获取失败返回"未知"
	 */
	private String getLocalIpAddress() {
		try {
			InetAddress localHost = InetAddress.getLocalHost();
			return localHost.getHostAddress();
		} catch (UnknownHostException e) {
			return "未知";
		}
	}

	/**
	 * 处理MES接口返回的生产单据同步响应，根据响应结果更新生产单据状态
	 *
	 * @param responseJson MES接口返回的响应
	 * @param prorequist   生产单据主表实体
	 * @param id           生产单据ID
	 * @return 处理后的结果JSON对象
	 */
	private JSONObject handleProductionResponse(JSONObject responseJson, WmsProrequist prorequist, String id) {
		log.info("生产单据同步结果: {}", responseJson.toJSONString());
		boolean isSuccess = responseJson.getBoolean("isSuccess");
		JSONObject result = new JSONObject();
		
		// 提取需要保存的消息内容
		String syncMessage = "";
		if (isSuccess) {
			JSONArray successEntities = responseJson.getJSONArray("successEntitys");
			if (successEntities != null && !successEntities.isEmpty()) {
				// 同步成功，更新生产单据状态
				syncMessage = "同步成功，单据已提交到上游系统";
				prorequist.setErpSync(WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue());
				prorequist.setUpdateTime(new Date());
				
				// 从successEntities中获取第一个元素的id和number并赋值
				if (successEntities.size() > 0) {
					JSONObject firstEntity = successEntities.getJSONObject(0);
					if (firstEntity != null) {
						// 将id和number分别赋值给k3Id和k3BillNo
						if (firstEntity.containsKey("id")) {
							prorequist.setK3Id(firstEntity.getString("id"));
						}
						if (firstEntity.containsKey("number")) {
							prorequist.setK3BillNo(firstEntity.getString("number"));
						}
					}
				}
				
				log.info("生产单据同步成功，ID: {}", id);
				result.put("success", true);
				result.put("message", "生产单据同步成功");
			}
		} else {
			JSONArray errors = responseJson.getJSONArray("errors");
			int msgCode = responseJson.getIntValue("msgCode");
			String fieldName = responseJson.getString("fieldName");
			String errorMessage = (errors != null) ? errors.toJSONString() : "未知错误";

			// 如接口返回特定错误码及字段，提示是否强制提交（例如库存不足）
			if (msgCode == 11 && "AbstractInteractionResult".equals(fieldName)) {
				syncMessage = "更新库存时出现可以忽略的异常数据";
				result.put("success", false);
				result.put("message", "更新库存时出现可以忽略的异常数据，是否继续？");
				result.put("forceSubmit", true);
			} else {
				syncMessage = "同步失败: " + errorMessage;
				log.error("其他生产单据同步失败，ID: {}，错误信息: {}", id, errorMessage);
				result.put("success", false);
				result.put("message", "其他生产单据同步失败: " + errorMessage);
				// 设置同步失败状态
				prorequist.setErpSync("3");
				prorequist.setUpdateTime(new Date());
			}
		}
		
		// 确保消息长度不超过数据库限制(VARCHAR(255))
		if (syncMessage.length() > 255) {
			syncMessage = syncMessage.substring(0, 252) + "...";
		}
		
		// 设置同步消息
		prorequist.setSyncMessage(syncMessage);
		wmsProrequistMapper.updateById(prorequist);
		
		return result;
	}
}

