package org.jeecg.modules.admin.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 直接调拨单
 * @Author: jeecg-boot
 * @Date:   2024-11-04
 * @Version: V1.0
 */
@Data
@TableName("wms_direct_transfer")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="wms_direct_transfer对象", description="直接调拨单")
public class WmsDirectTransfer implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**审核人*/
	@Excel(name = "审核人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
	@Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @ApiModelProperty(value = "审核人")
    private java.lang.String checkBy;
	/**审核日期*/
	@Excel(name = "审核日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核日期")
    private java.util.Date checkTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**wms调拨单号*/
	@Excel(name = "wms调拨单号", width = 15)
    @ApiModelProperty(value = "wms调拨单号")
    private java.lang.String billNo;
	/**K3调拨单号*/
	@Excel(name = "K3调拨单号", width = 15)
    @ApiModelProperty(value = "K3调拨单号")
    private java.lang.String workNo;
	/**单据状态*/
	@Excel(name = "单据状态", width = 15, dicCode = "wms_direct_transfer_bill_status")
	@Dict(dicCode = "wms_direct_transfer_bill_status")
    @ApiModelProperty(value = "单据状态")
    private java.lang.String billStatus;
	/**行号*/
	@Excel(name = "行号", width = 15)
    @ApiModelProperty(value = "行号")
    private java.lang.Integer lineNo;
	/**品号*/
	@Excel(name = "品号", width = 15)
    @ApiModelProperty(value = "品号")
    private java.lang.String itemCode;
	/**品名*/
	@Excel(name = "品名", width = 15)
    @ApiModelProperty(value = "品名")
    private java.lang.String itemName;
	/**规格*/
	@Excel(name = "规格", width = 15)
    @ApiModelProperty(value = "规格")
    private java.lang.String itemSpec;
	/**单位*/
	@Excel(name = "单位", width = 15)
    @ApiModelProperty(value = "单位")
    private java.lang.String itemUnit;
	/**日期*/
	@Excel(name = "日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "日期")
    private java.util.Date transferDate;
	/**调拨数量*/
	@Excel(name = "调拨数量", width = 15)
    @ApiModelProperty(value = "调拨数量")
    private java.lang.Double transferQty;
	/**调出仓库编号*/
	@Excel(name = "调出仓库编号", width = 15)
    @ApiModelProperty(value = "调出仓库编号")
    private java.lang.String warehouseCodeOut;
	/**调出仓库名称*/
	@Excel(name = "调出仓库名称", width = 15)
    @ApiModelProperty(value = "调出仓库名称")
    private java.lang.String warehouseOut;
	/**调入仓库编号*/
	@Excel(name = "调入仓库编号", width = 15)
    @ApiModelProperty(value = "调入仓库编号")
    private java.lang.String warehouseCodeIn;
	/**调入仓库名称*/
	@Excel(name = "调入仓库名称", width = 15)
    @ApiModelProperty(value = "调入仓库名称")
    private java.lang.String warehouseIn;
	/**erp同步状态*/
	@Excel(name = "erp同步状态", width = 15, dicCode = "erp_sync")
	@Dict(dicCode = "erp_sync")
    @ApiModelProperty(value = "erp同步状态")
    private java.lang.String erpSync;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
    /**库存id*/
    @Excel(name = "库存id", width = 15)
    @ApiModelProperty(value = "库存id")
    private java.lang.String stockId;
}
