package org.jeecg.modules.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description: 拣选
 * @Author: jeecg-boot
 * @Date:   2024-06-19
 * @Version: V1.0
 */
@Data
@ApiModel(value="wms_sendPage对象", description="拣选")
public class TaskPriorityPage {

	/**任务id*/
	@ApiModelProperty(value = "任务id")
    private String taskId;
	/**任务优先级*/
	@ApiModelProperty(value = "任务优先级")
    private String priorityCode;
}
