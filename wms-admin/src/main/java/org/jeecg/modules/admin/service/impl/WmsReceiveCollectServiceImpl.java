package org.jeecg.modules.admin.service.impl;

import org.jeecg.modules.admin.entity.WmsReceiveCollect;
import org.jeecg.modules.admin.mapper.WmsReceiveCollectMapper;
import org.jeecg.modules.admin.service.IWmsReceiveCollectService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 收料汇总
 * @Author: jeecg-boot
 * @Date:   2024-11-01
 * @Version: V1.0
 */
@Service
public class WmsReceiveCollectServiceImpl extends ServiceImpl<WmsReceiveCollectMapper, WmsReceiveCollect> implements IWmsReceiveCollectService {

}
