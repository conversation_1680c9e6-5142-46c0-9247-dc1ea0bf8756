package org.jeecg.modules.admin.controller;

import java.io.UnsupportedEncodingException;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.entity.WmsProrequistDetail;
import org.jeecg.modules.admin.entity.WmsProrequist;
import org.jeecg.modules.admin.vo.WmsProrequistPage;
import org.jeecg.modules.admin.service.IWmsProrequistService;
import org.jeecg.modules.admin.service.IWmsProrequistDetailService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.poi.excel.style.NoWrapExcelExportStyler;

 /**
 * @Description: 生产单据
 * @Author: jeecg-boot
 * @Date:   2024-11-06
 * @Version: V1.0
 */
@Api(tags="生产单据")
@RestController
@RequestMapping("/admin/wmsProrequist")
@Slf4j
public class WmsProrequistController {
	@Autowired
	private IWmsProrequistService wmsProrequistService;
	@Autowired
	private IWmsProrequistDetailService wmsProrequistDetailService;
	
	/**
	 * 分页列表查询
	 *
	 * @param wmsProrequist
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "生产单据-分页列表查询")
	@ApiOperation(value="生产单据-分页列表查询", notes="生产单据-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WmsProrequist>> queryPageList(WmsProrequist wmsProrequist,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WmsProrequist> queryWrapper = QueryGenerator.initQueryWrapper(wmsProrequist, req.getParameterMap());
		Page<WmsProrequist> page = new Page<WmsProrequist>(pageNo, pageSize);
		IPage<WmsProrequist> pageList = wmsProrequistService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param wmsProrequistPage
	 * @return
	 */
	@AutoLog(value = "生产单据-添加")
	@ApiOperation(value="生产单据-添加", notes="生产单据-添加")
    @RequiresPermissions("admin:wms_prorequist:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WmsProrequistPage wmsProrequistPage) {
		WmsProrequist wmsProrequist = new WmsProrequist();
		BeanUtils.copyProperties(wmsProrequistPage, wmsProrequist);
		wmsProrequistService.saveMain(wmsProrequist, wmsProrequistPage.getWmsProrequistDetailList());
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param wmsProrequistPage
	 * @return
	 */
	@AutoLog(value = "生产单据-编辑")
	@ApiOperation(value="生产单据-编辑", notes="生产单据-编辑")
    @RequiresPermissions("admin:wms_prorequist:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WmsProrequistPage wmsProrequistPage) {
		WmsProrequist wmsProrequist = new WmsProrequist();
		BeanUtils.copyProperties(wmsProrequistPage, wmsProrequist);
		WmsProrequist wmsProrequistEntity = wmsProrequistService.getById(wmsProrequist.getId());
		if(wmsProrequistEntity==null) {
			return Result.error("未找到对应数据");
		}
		wmsProrequistService.updateMain(wmsProrequist, wmsProrequistPage.getWmsProrequistDetailList());
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "生产单据-通过id删除")
	@ApiOperation(value="生产单据-通过id删除", notes="生产单据-通过id删除")
    @RequiresPermissions("admin:wms_prorequist:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		wmsProrequistService.delMain(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "生产单据-批量删除")
	@ApiOperation(value="生产单据-批量删除", notes="生产单据-批量删除")
    @RequiresPermissions("admin:wms_prorequist:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.wmsProrequistService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "生产单据-通过id查询")
	@ApiOperation(value="生产单据-通过id查询", notes="生产单据-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WmsProrequist> queryById(@RequestParam(name="id",required=true) String id) {
		WmsProrequist wmsProrequist = wmsProrequistService.getById(id);
		if(wmsProrequist==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(wmsProrequist);

	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "生产单据明细-通过主表ID查询")
	@ApiOperation(value="生产单据明细-通过主表ID查询", notes="生产单据明细-通过主表ID查询")
	@GetMapping(value = "/queryWmsProrequistDetailByMainId")
	public Result<IPage<WmsProrequistDetail>> queryWmsProrequistDetailListByMainId(@RequestParam(name="id",required=true) String id) {
		List<WmsProrequistDetail> wmsProrequistDetailList = wmsProrequistDetailService.selectByMainId(id);
		IPage <WmsProrequistDetail> page = new Page<>();
		page.setRecords(wmsProrequistDetailList);
		page.setTotal(wmsProrequistDetailList.size());
		return Result.OK(page);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param wmsProrequist
    */
    @RequiresPermissions("admin:wms_prorequist:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WmsProrequist wmsProrequist) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<WmsProrequist> queryWrapper = QueryGenerator.initQueryWrapper(wmsProrequist, request.getParameterMap());
      LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

     //配置选中数据查询条件
      String selections = request.getParameter("selections");
      if(oConvertUtils.isNotEmpty(selections)) {
           List<String> selectionList = Arrays.asList(selections.split(","));
           queryWrapper.in("id",selectionList);
      }
      //Step.2 获取导出数据
      List<WmsProrequist>  wmsProrequistList = wmsProrequistService.list(queryWrapper);

      // Step.3 组装pageList
      List<WmsProrequistPage> pageList = new ArrayList<WmsProrequistPage>();
      for (WmsProrequist main : wmsProrequistList) {
          List<WmsProrequistDetail> wmsProrequistDetailList = wmsProrequistDetailService.selectByMainId(main.getId());
          if(wmsProrequistDetailList != null && wmsProrequistDetailList.size() > 0) {
              // 修改导出方式：每个明细都创建一个完整的WmsProrequistPage对象
              for(WmsProrequistDetail detail : wmsProrequistDetailList) {
                  WmsProrequistPage vo = new WmsProrequistPage();
                  BeanUtils.copyProperties(main, vo);
                  List<WmsProrequistDetail> detailList = new ArrayList<>();
                  detailList.add(detail);
                  vo.setWmsProrequistDetailList(detailList);
                  pageList.add(vo);
              }
          } else {
              // 如果没有明细，仍然添加主表记录
              WmsProrequistPage vo = new WmsProrequistPage();
              BeanUtils.copyProperties(main, vo);
              vo.setWmsProrequistDetailList(new ArrayList<>());
              pageList.add(vo);
          }
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "生产单据列表");
      mv.addObject(NormalExcelConstants.CLASS, WmsProrequistPage.class);
      
      // 应用无换行样式，不设置标题
      ExportParams exportParams = new ExportParams(null, null, "生产单据");
      // 设置单元格不换行样式
      exportParams.setStyle(NoWrapExcelExportStyler.class);
      
      mv.addObject(NormalExcelConstants.PARAMS, exportParams);
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:wms_prorequist:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          // 获取上传文件对象
          MultipartFile file = entity.getValue();
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<WmsProrequistPage> list = ExcelImportUtil.importExcel(file.getInputStream(), WmsProrequistPage.class, params);
              for (WmsProrequistPage page : list) {
                  WmsProrequist po = new WmsProrequist();
                  BeanUtils.copyProperties(page, po);
                  wmsProrequistService.saveMain(po, page.getWmsProrequistDetailList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }

	 /**
	  * 提交生产单据
	  * @param id
	  */
	 @AutoLog(value = "生产单据-提交生产单据")
	 @ApiOperation(value="生产单据-提交生产单据", notes="生产单据-提交生产单据")
	 @GetMapping(value = "/submitRecord")
	 public Result<JSONObject> submitRecord(@RequestParam(name="id",required=true) String id, @RequestParam(name="flag",required=false) boolean flag) {
		 JSONObject jsonObject = wmsProrequistService.submitRecord(id, flag);
		 return Result.ok(jsonObject);
	 }

}
