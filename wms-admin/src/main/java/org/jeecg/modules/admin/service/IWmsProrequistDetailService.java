package org.jeecg.modules.admin.service;

import org.jeecg.modules.admin.entity.WmsProrequistDetail;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 生产单据明细
 * @Author: jeecg-boot
 * @Date:   2024-11-06
 * @Version: V1.0
 */
public interface IWmsProrequistDetailService extends IService<WmsProrequistDetail> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<WmsProrequistDetail>
	 */
	public List<WmsProrequistDetail> selectByMainId(String mainId);
}
