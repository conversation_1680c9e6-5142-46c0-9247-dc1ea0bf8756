package org.jeecg.modules.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 库存取消配货
 * @Author: jeecg-boot
 * @Date:   2024-06-19
 * @Version: V1.0
 */
@Data
@ApiModel(value="StockCancelPage", description="库存取消配货")
public class StockCancelPage {

	/**库存id*/
	@ApiModelProperty(value = "库存id")
    private String stockID;
	/**出库数量*/
	@ApiModelProperty(value = "出库数量")
	private Double quantity;
	/**lpn*/
	@ApiModelProperty(value = "lpn")
	private String lpn;
	/**货位id*/
	@ApiModelProperty(value = "货位id")
	private String locate_id;
	/**货位编号*/
	@ApiModelProperty(value = "货位编号")
	private String locate_code;
	/**库存状态*/
	@ApiModelProperty(value = "库存状态")
	private String inv_state;
}
