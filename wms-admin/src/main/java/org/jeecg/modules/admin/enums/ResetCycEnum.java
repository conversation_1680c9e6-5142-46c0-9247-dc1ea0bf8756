package org.jeecg.modules.admin.enums;

public enum ResetCycEnum {

    // 不重置
    BUCHONGZHI(0, "到达种子最大值重置"),
    // 按天
    ANTIAN(1, "按天重置"),
    // 按月
    ANYUE(2, "按月重置"),
    // 按年
    ANNIAN(3, "按年重置");

    private Integer code;

    private String name;

    ResetCycEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static ResetCycEnum fromCode(Integer code) {
        for (ResetCycEnum resetCyc : ResetCycEnum.values()) {
            if (resetCyc.getCode().equals(code)) {
                return resetCyc;
            }
        }
        return null; // 或者可以抛出异常
    }

}
