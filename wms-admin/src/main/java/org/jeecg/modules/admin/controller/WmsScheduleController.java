package org.jeecg.modules.admin.controller;

import java.io.UnsupportedEncodingException;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.entity.WmsScheduleDetail;
import org.jeecg.modules.admin.entity.WmsSchedule;
import org.jeecg.modules.admin.vo.WmsSchedulePage;
import org.jeecg.modules.admin.service.IWmsScheduleService;
import org.jeecg.modules.admin.service.IWmsScheduleDetailService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.poi.excel.style.NoWrapExcelExportStyler;

 /**
 * @Description: 排运单
 * @Author: jeecg-boot
 * @Date:   2024-10-26
 * @Version: V1.0
 */
@Api(tags="排运单")
@RestController
@RequestMapping("/admin/wmsSchedule")
@Slf4j
public class WmsScheduleController {
	@Autowired
	private IWmsScheduleService wmsScheduleService;
	@Autowired
	private IWmsScheduleDetailService wmsScheduleDetailService;
	
	/**
	 * 分页列表查询
	 *
	 * @param wmsSchedule
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "排运单-分页列表查询")
	@ApiOperation(value="排运单-分页列表查询", notes="排运单-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WmsSchedule>> queryPageList(WmsSchedule wmsSchedule,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WmsSchedule> queryWrapper = QueryGenerator.initQueryWrapper(wmsSchedule, req.getParameterMap());
		Page<WmsSchedule> page = new Page<WmsSchedule>(pageNo, pageSize);
		IPage<WmsSchedule> pageList = wmsScheduleService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param wmsSchedulePage
	 * @return
	 */
	@AutoLog(value = "排运单-添加")
	@ApiOperation(value="排运单-添加", notes="排运单-添加")
    @RequiresPermissions("admin:wms_schedule:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WmsSchedulePage wmsSchedulePage) {
		WmsSchedule wmsSchedule = new WmsSchedule();
		BeanUtils.copyProperties(wmsSchedulePage, wmsSchedule);
		wmsScheduleService.saveMain(wmsSchedule, wmsSchedulePage.getWmsScheduleDetailList());
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param wmsSchedulePage
	 * @return
	 */
	@AutoLog(value = "排运单-编辑")
	@ApiOperation(value="排运单-编辑", notes="排运单-编辑")
    @RequiresPermissions("admin:wms_schedule:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WmsSchedulePage wmsSchedulePage) {
		WmsSchedule wmsSchedule = new WmsSchedule();
		BeanUtils.copyProperties(wmsSchedulePage, wmsSchedule);
		WmsSchedule wmsScheduleEntity = wmsScheduleService.getById(wmsSchedule.getId());
		if(wmsScheduleEntity==null) {
			return Result.error("未找到对应数据");
		}
		wmsScheduleService.updateMain(wmsSchedule, wmsSchedulePage.getWmsScheduleDetailList());
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "排运单-通过id删除")
	@ApiOperation(value="排运单-通过id删除", notes="排运单-通过id删除")
    @RequiresPermissions("admin:wms_schedule:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		wmsScheduleService.delMain(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "排运单-批量删除")
	@ApiOperation(value="排运单-批量删除", notes="排运单-批量删除")
    @RequiresPermissions("admin:wms_schedule:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.wmsScheduleService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "排运单-通过id查询")
	@ApiOperation(value="排运单-通过id查询", notes="排运单-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WmsSchedule> queryById(@RequestParam(name="id",required=true) String id) {
		WmsSchedule wmsSchedule = wmsScheduleService.getById(id);
		if(wmsSchedule==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(wmsSchedule);

	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "排运单明细-通过主表ID查询")
	@ApiOperation(value="排运单明细-通过主表ID查询", notes="排运单明细-通过主表ID查询")
	@GetMapping(value = "/queryWmsScheduleDetailByMainId")
	public Result<IPage<WmsScheduleDetail>> queryWmsScheduleDetailListByMainId(@RequestParam(name="id",required=true) String id) {
		List<WmsScheduleDetail> wmsScheduleDetailList = wmsScheduleDetailService.selectByMainId(id);
		IPage <WmsScheduleDetail> page = new Page<>();
		page.setRecords(wmsScheduleDetailList);
		page.setTotal(wmsScheduleDetailList.size());
		return Result.OK(page);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param wmsSchedule
    */
    @RequiresPermissions("admin:wms_schedule:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WmsSchedule wmsSchedule) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<WmsSchedule> queryWrapper = QueryGenerator.initQueryWrapper(wmsSchedule, request.getParameterMap());
      LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

     //配置选中数据查询条件
      String selections = request.getParameter("selections");
      if(oConvertUtils.isNotEmpty(selections)) {
           List<String> selectionList = Arrays.asList(selections.split(","));
           queryWrapper.in("id",selectionList);
      }
      //Step.2 获取导出数据
      List<WmsSchedule>  wmsScheduleList = wmsScheduleService.list(queryWrapper);

      // Step.3 组装pageList
      List<WmsSchedulePage> pageList = new ArrayList<WmsSchedulePage>();
      for (WmsSchedule main : wmsScheduleList) {
          List<WmsScheduleDetail> wmsScheduleDetailList = wmsScheduleDetailService.selectByMainId(main.getId());
          if(wmsScheduleDetailList != null && wmsScheduleDetailList.size() > 0) {
              // 修改导出方式：每个明细都创建一个完整的WmsSchedulePage对象
              for(WmsScheduleDetail detail : wmsScheduleDetailList) {
                  WmsSchedulePage vo = new WmsSchedulePage();
                  BeanUtils.copyProperties(main, vo);
                  List<WmsScheduleDetail> detailList = new ArrayList<>();
                  detailList.add(detail);
                  vo.setWmsScheduleDetailList(detailList);
                  pageList.add(vo);
              }
          } else {
              // 如果没有明细，仍然添加主表记录
              WmsSchedulePage vo = new WmsSchedulePage();
              BeanUtils.copyProperties(main, vo);
              vo.setWmsScheduleDetailList(new ArrayList<>());
              pageList.add(vo);
          }
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "排运单列表");
      mv.addObject(NormalExcelConstants.CLASS, WmsSchedulePage.class);
      
      // 应用无换行样式，不设置标题
      ExportParams exportParams = new ExportParams(null, null, "排运单");
      // 设置单元格不换行样式
      exportParams.setStyle(NoWrapExcelExportStyler.class);
      
      mv.addObject(NormalExcelConstants.PARAMS, exportParams);
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:wms_schedule:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          // 获取上传文件对象
          MultipartFile file = entity.getValue();
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<WmsSchedulePage> list = ExcelImportUtil.importExcel(file.getInputStream(), WmsSchedulePage.class, params);
              for (WmsSchedulePage page : list) {
                  WmsSchedule po = new WmsSchedule();
                  BeanUtils.copyProperties(page, po);
                  wmsScheduleService.saveMain(po, page.getWmsScheduleDetailList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }

	 /**
	  * 11.1.1 WMS新增排运单接口
	  * 当对方以单表形式传递时，我们需要拆分为主表(WmsSchedule)+子表(WmsScheduleDetail)形式保存
	  */
//	 @AutoLog(value = "排运单-接收数据新增排运单")
//	 @ApiOperation(value="排运单-接收数据新增排运单", notes="排运单-接收数据新增排运单")
//	 @PostMapping(value = "/receiveSalesOut")
//	 public Result<?> receiveSalesOut(@RequestBody JSONObject jsonObj) {
//		 JSONArray dataArr = jsonObj.getJSONArray("data");
//		 wmsScheduleService.receiveSalesOut(dataArr);
//		 return Result.OK("销售出库单新增成功！");
//	 }

}
