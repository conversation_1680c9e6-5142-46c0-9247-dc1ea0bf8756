package org.jeecg.modules.admin.service;

import org.jeecg.modules.admin.bean.res.WmsInventoryCountTaskVo;
import org.jeecg.modules.admin.entity.WmsInventoryCountTask;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 盘点任务
 * @Author: jeecg-boot
 * @Date:   2024-12-20
 * @Version: V1.0
 */
public interface IWmsInventoryCountTaskService extends IService<WmsInventoryCountTask> {

    WmsInventoryCountTaskVo getCountTask(String param);

    void finishStockCheckTask(WmsInventoryCountTaskVo wmsInventoryCountTaskVo);

    List<WmsInventoryCountTask> getCountTaskByPlanId(String planId);
}
