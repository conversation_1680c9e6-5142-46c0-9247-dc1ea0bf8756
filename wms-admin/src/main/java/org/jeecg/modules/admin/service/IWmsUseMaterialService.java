package org.jeecg.modules.admin.service;

import org.jeecg.modules.admin.entity.WmsUseMaterialdetail;
import org.jeecg.modules.admin.entity.WmsUseMaterial;
import com.baomidou.mybatisplus.extension.service.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 用料清单
 * @Author: jeecg-boot
 * @Date:   2024-10-28
 * @Version: V1.0
 */
public interface IWmsUseMaterialService extends IService<WmsUseMaterial> {

	/**
	 * 添加一对多
	 *
	 * @param wmsUseMaterial
	 * @param wmsUseMaterialdetailList
	 */
	public void saveMain(WmsUseMaterial wmsUseMaterial,List<WmsUseMaterialdetail> wmsUseMaterialdetailList) ;
	
	/**
	 * 修改一对多
	 *
	 * @param wmsUseMaterial
	 * @param wmsUseMaterialdetailList
	 */
	public void updateMain(WmsUseMaterial wmsUseMaterial,List<WmsUseMaterialdetail> wmsUseMaterialdetailList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

	List<WmsUseMaterialdetail> selectByPorderNo(String porderNo);
}
