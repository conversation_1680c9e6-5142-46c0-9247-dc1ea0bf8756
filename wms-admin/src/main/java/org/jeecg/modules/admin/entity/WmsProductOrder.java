package org.jeecg.modules.admin.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 生产订单
 * @Author: jeecg-boot
 * @Date:   2024-10-26
 * @Version: V1.0
 */
@Data
@TableName("wms_product_order")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="wms_product_order对象", description="生产订单")
public class WmsProductOrder implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**单据编号*/
	@Excel(name = "单据编号", width = 15)
    @ApiModelProperty(value = "单据编号")
    private java.lang.String billNo;
	/**单据名称*/
	@Excel(name = "单据名称", width = 15)
    @ApiModelProperty(value = "单据名称")
    private java.lang.String billName;
	/**单据日期*/
	@Excel(name = "单据日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "单据日期")
    private java.util.Date billDate;
	/**生产组织*/
	@Excel(name = "生产组织", width = 15)
    @ApiModelProperty(value = "生产组织")
    private java.lang.String productOrganization;
	/**单据状态*/
	@Excel(name = "单据状态", width = 15, dicCode = "wms_product_order_bill_status")
	@Dict(dicCode = "wms_product_order_bill_status")
    @ApiModelProperty(value = "单据状态")
    private java.lang.String billStatus;
	/**是否来自ERP*/
	@Excel(name = "是否来自ERP", width = 15, dicCode = "from_erp")
	@Dict(dicCode = "from_erp")
    @ApiModelProperty(value = "是否来自ERP")
    private java.lang.String fromErp;
	/**ERP同步状态*/
	@Excel(name = "ERP同步状态", width = 15, dicCode = "erp_sync")
	@Dict(dicCode = "erp_sync")
    @ApiModelProperty(value = "ERP同步状态")
    private java.lang.String erpSync;
	/**业务类型*/
	@Excel(name = "业务类型", width = 15, dicCode = "wms_product_order_business_type")
	@Dict(dicCode = "wms_product_order_business_type")
    @ApiModelProperty(value = "业务类型")
    private java.lang.String businessType;
	/**业务状态*/
	@Excel(name = "业务状态", width = 15, dicCode = "wms_product_order_business_status")
	@Dict(dicCode = "wms_product_order_business_status")
    @ApiModelProperty(value = "业务状态")
    private java.lang.String businessStatus;
	/**生产车间*/
	@Excel(name = "生产车间", width = 15)
    @ApiModelProperty(value = "生产车间")
    private java.lang.String workShop;
    /**生产车间id*/
    @ApiModelProperty(value = "生产车间id")
    private java.lang.String workShopId;
    /**生产班组id*/
    @ApiModelProperty(value = "生产班组id")
    private java.lang.String workGroupId;
	/**生产班组*/
	@Excel(name = "生产班组", width = 15)
    @ApiModelProperty(value = "生产班组")
    private java.lang.String workGroup;
	/**生产班组编号*/
	@Excel(name = "生产班组编号", width = 15)
    @ApiModelProperty(value = "生产班组编号")
    private java.lang.String workGroupcode;
	/**品号*/
	@Excel(name = "品号", width = 15)
    @ApiModelProperty(value = "品号")
    private java.lang.String itemCode;
	/**品名*/
	@Excel(name = "品名", width = 15)
    @ApiModelProperty(value = "品名")
    private java.lang.String itemName;
	/**规格*/
	@Excel(name = "规格", width = 15)
    @ApiModelProperty(value = "规格")
    private java.lang.String itemSpec;
	/**单位*/
    @Excel(name = "单位", width = 15,dicCode = "item_unit")
    @ApiModelProperty(value = "单位")
    @Dict(dicCode = "item_unit")
    private String itemUnit;
	/**数量*/
	@Excel(name = "数量", width = 15, type = 4)
    @ApiModelProperty(value = "数量")
    private java.lang.Double quantity;
	/**计划开工时间*/
	@Excel(name = "计划开工时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "计划开工时间")
    private java.util.Date startTime;
	/**计划完工时间*/
	@Excel(name = "计划完工时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "计划完工时间")
    private java.util.Date finishTime;
	/**审核人*/
	@Excel(name = "审核人", width = 15)
    @ApiModelProperty(value = "审核人")
    private java.lang.String checkBy;
	/**审核日期*/
	@Excel(name = "审核日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核日期")
    private java.util.Date checkTime;
	/**单据备注*/
	@Excel(name = "单据备注", width = 15)
    @ApiModelProperty(value = "单据备注")
    private java.lang.String remark;
    /**生成正式单据*/
    @Excel(name = "生成正式单据", width = 15, dicCode = "yn")
    @Dict(dicCode = "yn")
    @ApiModelProperty(value = "生成正式单据")
    private java.lang.String formalBill;
}
