package org.jeecg.modules.admin.service;

import org.jeecg.modules.admin.entity.WmsReceivedetail;
import org.jeecg.modules.admin.entity.WmsReceive;
import com.baomidou.mybatisplus.extension.service.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @Description: 收货主单据
 * @Author: jeecg-boot
 * @Date:   2024-06-14
 * @Version: V1.0
 */
public interface IWmsReceiveService extends IService<WmsReceive> {

	/**
	 * 添加一对多
	 *
	 * @param wmsReceive
	 * @param wmsReceivedetailList
	 */
	public void saveMain(WmsReceive wmsReceive,List<WmsReceivedetail> wmsReceivedetailList) ;
	
	/**
	 * 修改一对多
	 *
	 * @param wmsReceive
	 * @param wmsReceivedetailList
	 */
	public void updateMain(WmsReceive wmsReceive,List<WmsReceivedetail> wmsReceivedetailList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

	Map<String, Object> getWeekReceive(String startTime, String endTime);

	Map<String, Object> getDayReceive(String date);

	/**
	 * 同步收料通知单入库
	 * @param id
	 */
	void syncReceive(String id);


}
