package org.jeecg.modules.admin.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.entity.WmsConttaskHis;
import org.jeecg.modules.admin.service.IWmsConttaskHisService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 历史任务表
 * @Author: jeecg-boot
 * @Date:   2024-07-30
 * @Version: V1.0
 */
@Api(tags="历史任务表")
@RestController
@RequestMapping("/admin/wmsConttaskHis")
@Slf4j
public class WmsConttaskHisController extends JeecgController<WmsConttaskHis, IWmsConttaskHisService> {
	@Autowired
	private IWmsConttaskHisService wmsConttaskHisService;
	
	/**
	 * 分页列表查询
	 *
	 * @param wmsConttaskHis
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "历史任务表-分页列表查询")
	@ApiOperation(value="历史任务表-分页列表查询", notes="历史任务表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WmsConttaskHis>> queryPageList(WmsConttaskHis wmsConttaskHis,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WmsConttaskHis> queryWrapper = QueryGenerator.initQueryWrapper(wmsConttaskHis, req.getParameterMap());
		Page<WmsConttaskHis> page = new Page<WmsConttaskHis>(pageNo, pageSize);
		IPage<WmsConttaskHis> pageList = wmsConttaskHisService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param wmsConttaskHis
	 * @return
	 */
	@AutoLog(value = "历史任务表-添加")
	@ApiOperation(value="历史任务表-添加", notes="历史任务表-添加")
	@RequiresPermissions("admin:wms_conttask_his:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WmsConttaskHis wmsConttaskHis) {
		wmsConttaskHisService.save(wmsConttaskHis);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param wmsConttaskHis
	 * @return
	 */
	@AutoLog(value = "历史任务表-编辑")
	@ApiOperation(value="历史任务表-编辑", notes="历史任务表-编辑")
	@RequiresPermissions("admin:wms_conttask_his:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WmsConttaskHis wmsConttaskHis) {
		wmsConttaskHisService.updateById(wmsConttaskHis);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "历史任务表-通过id删除")
	@ApiOperation(value="历史任务表-通过id删除", notes="历史任务表-通过id删除")
	@RequiresPermissions("admin:wms_conttask_his:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		wmsConttaskHisService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "历史任务表-批量删除")
	@ApiOperation(value="历史任务表-批量删除", notes="历史任务表-批量删除")
	@RequiresPermissions("admin:wms_conttask_his:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.wmsConttaskHisService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "历史任务表-通过id查询")
	@ApiOperation(value="历史任务表-通过id查询", notes="历史任务表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WmsConttaskHis> queryById(@RequestParam(name="id",required=true) String id) {
		WmsConttaskHis wmsConttaskHis = wmsConttaskHisService.getById(id);
		if(wmsConttaskHis==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(wmsConttaskHis);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param wmsConttaskHis
    */
    @RequiresPermissions("admin:wms_conttask_his:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WmsConttaskHis wmsConttaskHis) {
        return super.exportXls(request, wmsConttaskHis, WmsConttaskHis.class, "历史任务表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:wms_conttask_his:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WmsConttaskHis.class);
    }


}
