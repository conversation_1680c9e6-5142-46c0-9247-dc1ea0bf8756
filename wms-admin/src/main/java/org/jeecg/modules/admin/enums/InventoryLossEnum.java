package org.jeecg.modules.admin.enums;

/**
 * 盘点盈亏枚举
 */
public enum InventoryLossEnum {

    // 盘盈
    PROFIT("1", "盘盈"),
    // 盘亏
    LOSS("2", "盘亏");


    private String code;

    private String name;

    InventoryLossEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
