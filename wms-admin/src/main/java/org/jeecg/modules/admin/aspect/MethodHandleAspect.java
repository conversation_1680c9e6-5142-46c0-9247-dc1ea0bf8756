package org.jeecg.modules.admin.aspect;

import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.web.servlet.ShiroHttpServletRequest;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.jeecg.modules.admin.util.ToolUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.List;

@Aspect
@Component
@Slf4j
public class MethodHandleAspect {

    HttpServletRequest request;

    @Autowired
    public void setRequest(HttpServletRequest request) {
        this.request = request;
    }

    @Around("execution(* org.jeecg.modules.admin.controller..*(..))")
    public Object handleTimeAround(ProceedingJoinPoint point) throws Exception {
        String requestUrl = request.getRequestURI();
        Object[] args = point.getArgs();
        for (Object arg : args) {
            if(arg != null && arg instanceof HttpServletRequest) {
                request = (HttpServletRequest) arg;
                break;
            }
        }
        log.info("请求url:{}, 入参：\n{}", requestUrl, Arrays.toString(args));
        if(request != null && request instanceof ShiroHttpServletRequest) {
            ShiroHttpServletRequest shiroRequest = (ShiroHttpServletRequest) request;
            Enumeration<String> parameterNames = shiroRequest.getParameterNames();
            while (parameterNames.hasMoreElements()) {
                String paramName = parameterNames.nextElement();
                String paramValue = request.getParameter(paramName);
                log.info("请求参数: {} = {}", paramName, paramValue);
            }
        }

        // 开始时间
        LocalDateTime startTime = LocalDateTime.now();
        Object proceed = null;

        Exception exception = null;
        try {
            proceed = point.proceed();
        } catch (Throwable e) {
            exception = (Exception) e;
        }
        // 结束时间
        LocalDateTime endTime = LocalDateTime.now();
        // 用户获取时间差
        Duration duration = Duration.between(startTime, endTime);

        if (exception == null) {
            if(proceed instanceof List) {
                log.info("请求url：{}, 请求耗时：{}毫秒", requestUrl, duration.toMillis());
            } else {
                log.info("请求url：{}, 请求耗时：{}毫秒，请求回参：\n{}", requestUrl, duration.toMillis(), ToolUtils.writeValueAsString(proceed));
            }
        } else {
            throw exception;
        }

        return proceed;
    }
}
