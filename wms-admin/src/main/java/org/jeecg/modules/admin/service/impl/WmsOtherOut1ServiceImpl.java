package org.jeecg.modules.admin.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jeecg.common.api.dto.LogDTO;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.admin.entity.MesInterface;
import org.jeecg.modules.admin.entity.WmsOtherOut1;
import org.jeecg.modules.admin.entity.WmsOtherOut1Detail;
import org.jeecg.modules.admin.mapper.WmsOtherOut1DetailMapper;
import org.jeecg.modules.admin.mapper.WmsOtherOut1Mapper;
import org.jeecg.modules.admin.service.IMesInterfaceService;
import org.jeecg.modules.admin.service.IWmsOtherOut1Service;
import org.jeecg.modules.base.service.BaseCommonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.apache.shiro.SecurityUtils;
import org.jeecg.modules.admin.util.HttpClientUtil;

import java.io.IOException;
import java.io.Serializable;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 其他出库单
 * @Author: jeecg-boot
 * @Date:   2024-12-21
 * @Version: V1.0
 */
@Service
public class WmsOtherOut1ServiceImpl extends ServiceImpl<WmsOtherOut1Mapper, WmsOtherOut1> implements IWmsOtherOut1Service {
	private static final Logger log = LoggerFactory.getLogger(WmsOtherOut1ServiceImpl.class);
	@Autowired
	private WmsOtherOut1Mapper wmsOtherOut1Mapper;
	@Autowired
	private WmsOtherOut1DetailMapper wmsOtherOut1DetailMapper;
	@Autowired
	private StringRedisTemplate redisTemplate;
	@Autowired
	private BaseCommonService baseCommonService;
	@Autowired
	private IMesInterfaceService mesInterfaceService;

	// 每页大小，根据实际情况调优
	private static final int PAGE_SIZE = 5000;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(WmsOtherOut1 wmsOtherOut1, List<WmsOtherOut1Detail> wmsOtherOut1DetailList) {
		wmsOtherOut1Mapper.insert(wmsOtherOut1);
		if(wmsOtherOut1DetailList!=null && wmsOtherOut1DetailList.size()>0) {
			for(WmsOtherOut1Detail entity:wmsOtherOut1DetailList) {
				//外键设置
				entity.setBillId(wmsOtherOut1.getId());
				wmsOtherOut1DetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(WmsOtherOut1 wmsOtherOut1,List<WmsOtherOut1Detail> wmsOtherOut1DetailList) {
		wmsOtherOut1Mapper.updateById(wmsOtherOut1);
		
		//1.先删除子表数据
		wmsOtherOut1DetailMapper.deleteByMainId(wmsOtherOut1.getId());
		
		//2.子表数据重新插入
		if(wmsOtherOut1DetailList!=null && wmsOtherOut1DetailList.size()>0) {
			for(WmsOtherOut1Detail entity:wmsOtherOut1DetailList) {
				//外键设置
				entity.setBillId(wmsOtherOut1.getId());
				wmsOtherOut1DetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		wmsOtherOut1DetailMapper.deleteByMainId(id);
		wmsOtherOut1Mapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			wmsOtherOut1DetailMapper.deleteByMainId(id.toString());
			wmsOtherOut1Mapper.deleteById(id);
		}
	}

	/**
	 * 主动请求MES接口(领料申请单-其他出库单)获取数据, 并转换存到 主表(WmsOtherOut1) + 子表(WmsOtherOut1Detail).
	 */
	@Override
	public void syncOtherOut1() {
		// 1. 获取token
		String accessToken = redisTemplate.opsForValue().get("mes_login_access_token");
		if (accessToken == null) {
			throw new RuntimeException("未获取到MES登录token，请先调用登录接口！");
		}

		// 2. 从 mes_interface 表获取接口配置信息, 这里 code=JK017
		MesInterface mi = mesInterfaceService.getOne(
				new QueryWrapper<MesInterface>().eq("interface_code", "JK017")
		);
		if(mi == null) {
			throw new RuntimeException("未找到MES接口配置信息: JK017");
		}
		// 校验启用状态 & 请求方式
		if(!"1".equals(mi.getInterfaceStatus())) {
			throw new RuntimeException("接口JK017未启用！");
		}
		if(!"POST".equalsIgnoreCase(mi.getRequestMethod())) {
			throw new RuntimeException("接口JK017的请求方式不是POST！");
		}
		
		// 获取时间范围
		String[] dateRange = getDateRange(mi.getInterfaceCode());

		// 3. 先获取 total
		int total = getTotalCount(mi, accessToken, dateRange);
		if(total == 0) {
			log.info("领料申请单接口(JK017)无数据可同步");
			return;
		}
		int totalPages = (int)Math.ceil((double)total / PAGE_SIZE);
		log.info("领料申请单接口(JK017): 总数={}, 分页数量={}", total, totalPages);

		// 统计同步结果
		int successCount = 0;
		int failCount = 0;
		
		// 4. 分页获取 & 处理
		for(int pageNum = 1; pageNum <= totalPages; pageNum++) {
			List<JSONObject> rowList = fetchDataByPage(mi, accessToken, pageNum, dateRange);
			log.info("处理第{}页数据，共{}条", pageNum, rowList.size());
			
			// 按单据号分组处理，每个单据单独处理保存
			Map<String, List<JSONObject>> groupResult = processPageData(rowList);
			
			// 统计处理结果
			successCount += groupResult.getOrDefault("success", Collections.emptyList()).size();
			failCount += groupResult.getOrDefault("fail", Collections.emptyList()).size();
			
			log.info("第{}页处理完成，成功：{}，失败：{}", pageNum, 
				groupResult.getOrDefault("success", Collections.emptyList()).size(),
				groupResult.getOrDefault("fail", Collections.emptyList()).size());
		}
		
		log.info("领料申请单同步完成，总成功：{}，总失败：{}", successCount, failCount);
		
		// 5. 同步完成后，更新Redis中的开始时间为当前时间
		updateStartTime(mi.getInterfaceCode());
	}
	
	/**
	 * 获取同步的时间范围
	 * @param interfaceCode 接口编码
	 * @return 时间范围数组 [开始时间, 结束时间]
	 */
	private String[] getDateRange(String interfaceCode) {
		String redisKey = interfaceCode + "_startTime";
		String startTimeStr = redisTemplate.opsForValue().get(redisKey);
		
		LocalDate now = LocalDate.now();
		String endTimeStr = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
		
		// 如果Redis中没有开始时间，则默认为当年1月1日
		if (startTimeStr == null) {
			startTimeStr = now.withDayOfYear(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
		}
		
		return new String[]{startTimeStr, endTimeStr};
	}
	
	/**
	 * 更新Redis中的开始时间
	 * @param interfaceCode 接口编码
	 */
	private void updateStartTime(String interfaceCode) {
		String redisKey = interfaceCode + "_startTime";
		String currentTime = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
		redisTemplate.opsForValue().set(redisKey, currentTime);
		log.info("更新接口{}下次同步的开始时间为: {}", interfaceCode, currentTime);
	}

	/**
	 * 处理页面数据，按单据号分组并单独处理每个单据
	 * @param rowList 一页数据
	 * @return 处理结果，包含成功和失败的单据ID列表
	 */
	private Map<String, List<JSONObject>> processPageData(List<JSONObject> rowList) {
		if (rowList == null || rowList.isEmpty()) {
			return Collections.emptyMap();
		}

		// 按billNo分组
		Map<String, List<JSONObject>> groupedByBillNo = rowList.stream()
				.collect(Collectors.groupingBy(obj -> obj.getString("fbillno")));
				
		List<JSONObject> successList = new ArrayList<>();
		List<JSONObject> failList = new ArrayList<>();
		Map<String, List<JSONObject>> result = new HashMap<>();

		// 批量查询现有的billNo
		Set<String> allBillNos = groupedByBillNo.keySet();
		Set<String> existingBillNos = getExistingBillNos(allBillNos);

		// 处理每个单据，单独事务
		for (Map.Entry<String, List<JSONObject>> entry : groupedByBillNo.entrySet()) {
			String billNo = entry.getKey();
			List<JSONObject> groupedRows = entry.getValue();
			
			try {
				// 单独事务处理每个单据
				processSingleBill(billNo, groupedRows, existingBillNos);
				// 处理成功，记录
				JSONObject successInfo = new JSONObject();
				successInfo.put("billNo", billNo);
				successList.add(successInfo);
			} catch (Exception e) {
				// 处理失败，记录错误信息但不影响其他单据
				log.error("处理单据{}失败: {}", billNo, e.getMessage(), e);
				JSONObject failInfo = new JSONObject();
				failInfo.put("billNo", billNo);
				failInfo.put("errorMsg", e.getMessage());
				failList.add(failInfo);
			}
		}
		
		result.put("success", successList);
		result.put("fail", failList);
		return result;
	}
	
	/**
	 * 处理单个单据(独立事务)
	 */
	@Transactional(rollbackFor = Exception.class)
	public void processSingleBill(String billNo, List<JSONObject> groupedRows, Set<String> existingBillNos) {
		if (groupedRows == null || groupedRows.isEmpty()) {
			return;
		}

		// 使用线程安全的DateTimeFormatter
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
		LocalDateTime now = LocalDateTime.now();

		// 获取当前时间
		LocalDateTime currentTime = now;

		// 创建主表对象
		JSONObject firstObj = groupedRows.get(0);
		WmsOtherOut1 main = new WmsOtherOut1();
		main.setBillNo(billNo);
		main.setApplyOrganization(firstObj.getString("fapplicationorgid"));

		// 解析申请日期
		String fdateStr = firstObj.getString("fdate");
		if (fdateStr != null) {
			try {
				LocalDateTime requestDate = LocalDateTime.parse(fdateStr, formatter);
				main.setRequestDate(Date.from(requestDate.atZone(ZoneId.systemDefault()).toInstant()));
			} catch (Exception ex) {
				log.warn("日期解析失败: {}", ex.getMessage());
			}
		}

		// 设置单据状态，默认值优化
		String docStatus = firstObj.getString("fdocumentstatus");
		main.setBillStatus(docStatus != null ? docStatus : "1");

		main.setApplyDept(firstObj.getString("fbijcostgroupid"));
		main.setApplyBy(firstObj.getString("fapplicantid"));
		main.setRequestBy(firstObj.getString("fpickerid"));
		main.setRemark(firstObj.getString("fremarks"));
		main.setBillName("领料申请单");
		main.setErpSync("0");
		main.setBillType("LLSQD");
		main.setFromErp("1");
		main.setCreateTime(Date.from(currentTime.atZone(ZoneId.systemDefault()).toInstant()));
		main.setUpdateTime(Date.from(currentTime.atZone(ZoneId.systemDefault()).toInstant()));

		// 创建子表集合
		List<WmsOtherOut1Detail> details = new ArrayList<>();
		for (int i = 0; i < groupedRows.size(); i++) {
			JSONObject obj = groupedRows.get(i);
			WmsOtherOut1Detail detail = new WmsOtherOut1Detail();
			detail.setSerialNumber(i + 1);
			detail.setBillNo(billNo);
			detail.setStockIssue(obj.getString("fstockorgid"));
			detail.setWarehouse(obj.getString("fstockid"));
			detail.setCargoOwner(obj.getString("fownerid"));
			detail.setItemCode(obj.getString("fmaterialnumber"));
			detail.setItemName(obj.getString("fmaterialname"));
			detail.setItemUnit(obj.getString("funitname"));
			detail.setUseWay(obj.getString("fuseway"));
			detail.setPlanQty(obj.getDouble("fbaseqty"));
			detail.setDisQty(0.0);
			detail.setActQty(0.0);
			
			// 添加原单id和分录行id
			if (obj.containsKey("fid") && obj.get("fid") != null) {
				detail.setFid(obj.getLong("fid"));
			}
			if (obj.containsKey("fentryid") && obj.get("fentryid") != null) {
				detail.setFentryid(obj.getLong("fentryid"));
			}
			
			detail.setCreateTime(Date.from(currentTime.atZone(ZoneId.systemDefault()).toInstant()));
			detail.setUpdateTime(Date.from(currentTime.atZone(ZoneId.systemDefault()).toInstant()));
			details.add(detail);
		}

		// 判断是否存在，执行更新或保存
		if (existingBillNos.contains(billNo)) {
			// 更新操作
			this.updateMain(main, details);
			log.debug("更新单据: {}", billNo);
		} else {
			// 保存操作
			this.saveMain(main, details);
			log.debug("新增单据: {}", billNo);
		}
	}

	private Set<String> getExistingBillNos(Set<String> billNos) {
		if (billNos == null || billNos.isEmpty()) {
			return Collections.emptySet();
		}
		List<WmsOtherOut1> existingList = wmsOtherOut1Mapper.selectList(
				new QueryWrapper<WmsOtherOut1>().in("bill_no", billNos)
		);
		return existingList.stream().map(WmsOtherOut1::getBillNo).collect(Collectors.toSet());
	}

	/**
	 * 获取 total
	 */
	private int getTotalCount(MesInterface mi, String token, String[] dateRange) {
		JSONObject requestParam = new JSONObject();
		requestParam.put("pageNo", 1);
		requestParam.put("pageSize", 1);
		requestParam.put("fdate", dateRange);
		JSONObject resp = doPost(mi.getInterfaceUrl(), token, requestParam);
		return resp.getIntValue("total");
	}

	/**
	 * 分页获取
	 */
	private List<JSONObject> fetchDataByPage(MesInterface mi, String token, int pageNo, String[] dateRange) {
		JSONObject requestParam = new JSONObject();
		requestParam.put("pageNo", pageNo);
		requestParam.put("pageSize", PAGE_SIZE);
		requestParam.put("fdate", dateRange);
		JSONObject resp = doPost(mi.getInterfaceUrl(), token, requestParam);
		JSONArray rows = resp.getJSONArray("rows");
		if(rows == null || rows.isEmpty()) {
			return Collections.emptyList();
		}
		List<JSONObject> list = new ArrayList<>(rows.size());
		for(int i=0; i< rows.size(); i++){
			list.add(rows.getJSONObject(i));
		}
		return list;
	}

	/**
	 * 通用的POST请求
	 */
	private JSONObject doPost(String url, String token, JSONObject requestBody) {
		return HttpClientUtil.doPostEnhanced(url, token, requestBody, baseCommonService, "MES其他出库", "syncOtherOutData");
	}

	private String getLocalIpAddress() {
		try {
			InetAddress localHost = InetAddress.getLocalHost();
			return localHost.getHostAddress();
		} catch (UnknownHostException e) {
			return "未知";
		}
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public JSONObject submitRecord(String id, boolean b, String stockDirection) throws RuntimeException {
		log.info("开始提交其他出库单，ID: {}", id);
		// 根据id获取对应的数据
		WmsOtherOut1 wmsOtherOut1 = wmsOtherOut1Mapper.selectById(id);
		List<WmsOtherOut1Detail> wmsOtherOut1Details = wmsOtherOut1DetailMapper.selectByMainId(id);

		if (wmsOtherOut1 == null) {
			throw new RuntimeException("未找到对应的其他出库单数据: " + id);
		}

		// 根据单据状态判断是否可以提交
//		if (!"3".equals(wmsOtherOut1.getBillStatus())) {
//			throw new RuntimeException("其他出库单为非完成状态，无法提交！");
//		}

		// 根据erpSync判断是否已同步
		if ("2".equals(wmsOtherOut1.getErpSync())) {
			throw new RuntimeException("其他出库单已同步，无需重复提交！");
		}

		// 获取接口配置信息
		MesInterface mi = mesInterfaceService.getOne(
				new QueryWrapper<MesInterface>().eq("interface_code", "JK019")
		);
		if (mi == null) {
			throw new RuntimeException("未找到MES接口配置信息: JK019");
		}

		// 校验启用状态 & 请求方式
		if (!"1".equals(mi.getInterfaceStatus())) {
			throw new RuntimeException("接口JK019未启用！");
		}
		if (!"POST".equalsIgnoreCase(mi.getRequestMethod())) {
			throw new RuntimeException("接口JK019的请求方式不是POST！");
		}

		// 获取token
		String accessToken = redisTemplate.opsForValue().get("mes_login_access_token");
		if (accessToken == null) {
			throw new RuntimeException("未获取到MES登录token，请先调用登录接口！");
		}

		// 构建请求参数
		JSONObject requestParam = buildRequestParam(wmsOtherOut1, wmsOtherOut1Details,b,stockDirection);

		// 调用接口
		JSONObject jsonObject = doPost(mi.getInterfaceUrl(), accessToken, requestParam);

		// 处理响应
		return handleResponse(jsonObject, wmsOtherOut1, id);
	}

	/**
	 * 构建请求参数
	 */
	private JSONObject buildRequestParam(WmsOtherOut1 wmsOtherOut1, List<WmsOtherOut1Detail> details, boolean b, String stockDirection) {
		//获取当前登录用户
		LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		JSONObject requestParam = new JSONObject();

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String dateStr = sdf.format(wmsOtherOut1.getRequestDate());
		requestParam.put("date", dateStr);

		// 设置库存方向（如果为空或null则使用默认值 "GENERAL"）
		requestParam.put("fstockdirect", (stockDirection == null || stockDirection.isEmpty()) ? "GENERAL" : stockDirection);

		// 设置客户ID
		requestParam.put("fcustid", "440985");

		// 设置领料部门ID
		requestParam.put("fbijcostgroupid", wmsOtherOut1.getApplyDept() != null ? wmsOtherOut1.getApplyDept() : "");

		// 设置ignoreInterationFlag
		requestParam.put("ignoreInterationFlag", b);

		// 设置领料人ID
		requestParam.put("fpickerid", wmsOtherOut1.getRequestBy() != null ? wmsOtherOut1.getRequestBy() : "");

		// 设置备注
		requestParam.put("fnote", wmsOtherOut1.getRemark() != null ? wmsOtherOut1.getRemark() : "");

		// 设置创建人ID
		requestParam.put("creator", loginUser.getUsername());

		// 设置审核人ID
		requestParam.put("auditor", wmsOtherOut1.getCheckBy() != null ? wmsOtherOut1.getCheckBy() : "");
		requestParam.put("billNo", wmsOtherOut1.getBillNo());

		// 设置物料明细
		JSONArray fpurmrbentrys = new JSONArray();
		for (WmsOtherOut1Detail detail : details) {
			JSONObject detailObj = new JSONObject();
			detailObj.put("fmaterialnumber", detail.getItemCode());
			detailObj.put("fqty", detail.getActQty());
			detailObj.put("fstockid",detail.getWarehouse());
			
			// 只有当fid和fentryid都存在时才添加linkList
			if (detail.getFid() != null && detail.getFentryid() != null) {
				JSONArray linkList = new JSONArray();
				JSONObject linkObj = new JSONObject();
				linkObj.put("fsbillid", detail.getFid());
				linkObj.put("fsid", detail.getFentryid());
				linkList.add(linkObj);
				detailObj.put("linkList", linkList);
			}
			
			fpurmrbentrys.add(detailObj);
		}
		
		requestParam.put("fpurmrbentrys", fpurmrbentrys);

		return requestParam;
	}

	private JSONObject handleResponse(JSONObject responseJson, WmsOtherOut1 wmsOtherOut1, String id) {
		log.info("其他出库单同步结果: {}", responseJson.toJSONString());
		boolean isSuccess = responseJson.getBoolean("isSuccess");
		JSONObject result = new JSONObject();
		
		// 提取需要保存的消息内容
		String syncMessage = "";
		if (isSuccess) {
			// 处理成功响应
			JSONArray successEntities = responseJson.getJSONArray("successEntitys");
			if (successEntities != null && !successEntities.isEmpty()) {
				// 更新erpSync为已同步
				syncMessage = "同步成功，单据已提交到上游系统";
				wmsOtherOut1.setErpSync("2");
				wmsOtherOut1.setUpdateTime(new Date());
				
				// 从successEntities中获取第一个元素的id和number并赋值
				if (successEntities.size() > 0) {
					JSONObject firstEntity = successEntities.getJSONObject(0);
					if (firstEntity != null) {
						// 将id和number分别赋值给k3Id和k3BillNo
						if (firstEntity.containsKey("id")) {
							wmsOtherOut1.setK3Id(firstEntity.getString("id"));
						}
						if (firstEntity.containsKey("number")) {
							wmsOtherOut1.setK3BillNo(firstEntity.getString("number"));
						}
					}
				}
				
				log.info("其他出库单同步成功，ID: {}", id);
				result.put("success", true);
				result.put("message", "其他出库单同步成功");
			}
		} else {
			JSONArray errors = responseJson.getJSONArray("errors");
			int msgCode = responseJson.getIntValue("msgCode");
			
			// 默认错误信息
			String errorMessage = "未知错误";
			
			// 判断是否可以强制提交的标志
			boolean canForceSubmit = false;
			
			// 从errors中提取具体的错误信息
			if (errors != null && !errors.isEmpty()) {
				for (int i = 0; i < errors.size(); i++) {
					JSONObject error = errors.getJSONObject(i);
					// 检查fieldName是否为AbstractInteractionResult
					if ("AbstractInteractionResult".equals(error.getString("fieldName"))) {
						// 如果有message字段，优先使用它作为错误信息
						if (error.containsKey("message")) {
							errorMessage = error.getString("message");
						}
						
						// 如果msgCode为11且fieldName为AbstractInteractionResult，则可以强制提交
						if (msgCode == 11) {
							canForceSubmit = true;
						}
					}
				}
				
				// 如果没有找到AbstractInteractionResult，则使用整个errors数组作为错误信息
				if (errorMessage.equals("未知错误")) {
					errorMessage = errors.toJSONString();
				}
			}
			
			if (canForceSubmit) {
				// 可以强制提交的情况
				syncMessage = errorMessage;
				result.put("success", false);
				result.put("message", errorMessage);
				// 强制提交标记
				result.put("forceSubmit", true);
			} else {
				// 不可强制提交的情况
				syncMessage = "同步失败: " + errorMessage;
				log.error("其他出库单同步失败，ID: {}，错误信息: {}", id, errorMessage);
				result.put("success", false);
				result.put("message", errorMessage);
				// 设置erpSync为同步失败
				wmsOtherOut1.setErpSync("3");
				wmsOtherOut1.setUpdateTime(new Date());
			}
		}
		
		// 确保消息长度不超过数据库限制(VARCHAR(255))
		if (syncMessage.length() > 255) {
			syncMessage = syncMessage.substring(0, 252) + "...";
		}
		
		// 设置同步消息
		wmsOtherOut1.setSyncMessage(syncMessage);
		wmsOtherOut1Mapper.updateById(wmsOtherOut1);
		
		return result;
	}

	/**
	 * 处理单页数据, 将单表结构转换为主表+子表, 并存库(带@Transactional以便边请求边写入)
	 * @deprecated 替换为更细粒度的处理方法
	 */
	@Deprecated
	@Transactional(rollbackFor = Exception.class)
	public void processOnePage(List<JSONObject> rowList) {
		if (rowList == null || rowList.isEmpty()) {
			return;
		}

		// 使用线程安全的DateTimeFormatter
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
		LocalDateTime now = LocalDateTime.now();

		// 按billNo分组
		Map<String, List<JSONObject>> groupedByBillNo = rowList.stream()
				.collect(Collectors.groupingBy(obj -> obj.getString("fbillno")));

		// 获取所有billNo
		Set<String> allBillNos = groupedByBillNo.keySet();

		// 批量查询现有的billNo
		Set<String> existingBillNos = getExistingBillNos(allBillNos);

		for (Map.Entry<String, List<JSONObject>> entry : groupedByBillNo.entrySet()) {
			String billNo = entry.getKey();
			List<JSONObject> groupedRows = entry.getValue();

			// 获取当前时间
			LocalDateTime currentTime = now;

			// 创建主表对象
			JSONObject firstObj = groupedRows.get(0);
			WmsOtherOut1 main = new WmsOtherOut1();
			main.setBillNo(billNo);
			main.setApplyOrganization(firstObj.getString("fapplicationorgid"));

			// 解析申请日期
			String fdateStr = firstObj.getString("fdate");
			if (fdateStr != null) {
				try {
					LocalDateTime requestDate = LocalDateTime.parse(fdateStr, formatter);
					main.setRequestDate(Date.from(requestDate.atZone(ZoneId.systemDefault()).toInstant()));
				} catch (Exception ex) {
					// 假设有日志记录
					System.err.println("日期解析失败: " + ex.getMessage());
				}
			}

			// 设置单据状态，默认值优化
			String docStatus = firstObj.getString("fdocumentstatus");
			main.setBillStatus(docStatus != null ? docStatus : "1");

			main.setApplyDept(firstObj.getString("fapplicationdeptid"));
			main.setApplyBy(firstObj.getString("fapplicantid"));
			main.setRequestBy(firstObj.getString("fpickerid"));
			main.setRemark(firstObj.getString("fremarks"));
			main.setBillName("领料申请单");
			main.setErpSync("0");
			main.setBillType("LLSQD");
			main.setFromErp("1");
			main.setCreateTime(Date.from(currentTime.atZone(ZoneId.systemDefault()).toInstant()));
			main.setUpdateTime(Date.from(currentTime.atZone(ZoneId.systemDefault()).toInstant()));

			// 创建子表集合
			List<WmsOtherOut1Detail> details = new ArrayList<>();
			for (int i = 0; i < groupedRows.size(); i++) {
				JSONObject obj = groupedRows.get(i);
				WmsOtherOut1Detail detail = new WmsOtherOut1Detail();
				detail.setSerialNumber(i + 1);
				detail.setBillNo(billNo);
				detail.setStockIssue(obj.getString("fstockorgid"));
				detail.setWarehouse(obj.getString("fstockid"));
				detail.setCargoOwner(obj.getString("fownerid"));
				detail.setItemCode(obj.getString("fmaterialnumber"));
				detail.setItemName(obj.getString("fmaterialname"));
				detail.setItemUnit(obj.getString("funitname"));
				detail.setUseWay(obj.getString("fuseway"));
				detail.setPlanQty(obj.getDouble("fbaseqty"));
				detail.setDisQty(0.0);
				detail.setActQty(0.0);
				detail.setCreateTime(Date.from(currentTime.atZone(ZoneId.systemDefault()).toInstant()));
				detail.setUpdateTime(Date.from(currentTime.atZone(ZoneId.systemDefault()).toInstant()));
				details.add(detail);
			}

			// 判断是否存在，执行更新或保存
			if (existingBillNos.contains(billNo)) {
				// 更新操作
				this.updateMain(main, details);
			} else {
				// 保存操作
				this.saveMain(main, details);
			}
		}
	}
}

