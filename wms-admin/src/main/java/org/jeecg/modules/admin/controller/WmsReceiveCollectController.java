package org.jeecg.modules.admin.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.entity.WmsReceiveCollect;
import org.jeecg.modules.admin.entity.WmsReceivedetail;
import org.jeecg.modules.admin.service.IWmsReceiveCollectService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.admin.service.IWmsReceiveService;
import org.jeecg.modules.admin.service.IWmsReceivedetailService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 收货明细
 * @Author: jeecg-boot
 * @Date:   2024-11-01
 * @Version: V1.0
 */
@Api(tags="收货明细")
@RestController
@RequestMapping("/admin/wmsReceiveCollect")
@Slf4j
public class WmsReceiveCollectController extends JeecgController<WmsReceivedetail, IWmsReceivedetailService> {
	@Autowired
	private IWmsReceiveCollectService wmsReceiveCollectService;
	
	/**
	 * 分页列表查询
	 *
	 * @param wmsReceiveCollect
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "收货明细-分页列表查询")
	@ApiOperation(value="收货明细-分页列表查询", notes="收货明细-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WmsReceiveCollect>> queryPageList(WmsReceiveCollect wmsReceiveCollect,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WmsReceiveCollect> queryWrapper = QueryGenerator.initQueryWrapper(wmsReceiveCollect, req.getParameterMap());
		Page<WmsReceiveCollect> page = new Page<WmsReceiveCollect>(pageNo, pageSize);
		IPage<WmsReceiveCollect> pageList = wmsReceiveCollectService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param wmsReceiveCollect
	 * @return
	 */
	@AutoLog(value = "收货明细-添加")
	@ApiOperation(value="收货明细-添加", notes="收货明细-添加")
	@RequiresPermissions("admin:wms_receive_collect:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WmsReceiveCollect wmsReceiveCollect) {
		wmsReceiveCollectService.save(wmsReceiveCollect);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param wmsReceiveCollect
	 * @return
	 */
	@AutoLog(value = "收货明细-编辑")
	@ApiOperation(value="收货明细-编辑", notes="收货明细-编辑")
	@RequiresPermissions("admin:wms_receive_collect:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WmsReceiveCollect wmsReceiveCollect) {
		wmsReceiveCollectService.updateById(wmsReceiveCollect);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "收货明细-通过id删除")
	@ApiOperation(value="收货明细-通过id删除", notes="收货明细-通过id删除")
	@RequiresPermissions("admin:wms_receive_collect:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		wmsReceiveCollectService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "收货明细-批量删除")
	@ApiOperation(value="收货明细-批量删除", notes="收货明细-批量删除")
	@RequiresPermissions("admin:wms_receive_collect:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.wmsReceiveCollectService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "收货明细-通过id查询")
	@ApiOperation(value="收货明细-通过id查询", notes="收货明细-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WmsReceiveCollect> queryById(@RequestParam(name="id",required=true) String id) {
		WmsReceiveCollect wmsReceiveCollect = wmsReceiveCollectService.getById(id);
		if(wmsReceiveCollect==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(wmsReceiveCollect);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param wmsReceiveCollect
    */
    @RequiresPermissions("admin:wms_receive_collect:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WmsReceivedetail wmsReceiveCollect) {
        return super.exportXls(request, wmsReceiveCollect, WmsReceivedetail.class, "收货明细");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:wms_receive_collect:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WmsReceivedetail.class);
    }

}
