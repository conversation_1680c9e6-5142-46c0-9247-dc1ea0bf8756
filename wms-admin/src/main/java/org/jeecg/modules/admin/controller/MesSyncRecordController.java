package org.jeecg.modules.admin.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.entity.MesSyncRecord;
import org.jeecg.modules.admin.service.IMesSyncRecordService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: mes同步记录
 * @Author: jeecg-boot
 * @Date:   2024-12-12
 * @Version: V1.0
 */
@Api(tags="mes同步记录")
@RestController
@RequestMapping("/admin/mesSyncRecord")
@Slf4j
public class MesSyncRecordController extends JeecgController<MesSyncRecord, IMesSyncRecordService> {
	@Autowired
	private IMesSyncRecordService mesSyncRecordService;
	
	/**
	 * 分页列表查询
	 *
	 * @param mesSyncRecord
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "mes同步记录-分页列表查询")
	@ApiOperation(value="mes同步记录-分页列表查询", notes="mes同步记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<MesSyncRecord>> queryPageList(MesSyncRecord mesSyncRecord,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<MesSyncRecord> queryWrapper = QueryGenerator.initQueryWrapper(mesSyncRecord, req.getParameterMap());
		Page<MesSyncRecord> page = new Page<MesSyncRecord>(pageNo, pageSize);
		IPage<MesSyncRecord> pageList = mesSyncRecordService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param mesSyncRecord
	 * @return
	 */
	@AutoLog(value = "mes同步记录-添加")
	@ApiOperation(value="mes同步记录-添加", notes="mes同步记录-添加")
	@RequiresPermissions("admin:mes_sync_record:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody MesSyncRecord mesSyncRecord) {
		mesSyncRecordService.save(mesSyncRecord);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param mesSyncRecord
	 * @return
	 */
	@AutoLog(value = "mes同步记录-编辑")
	@ApiOperation(value="mes同步记录-编辑", notes="mes同步记录-编辑")
	@RequiresPermissions("admin:mes_sync_record:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody MesSyncRecord mesSyncRecord) {
		mesSyncRecordService.updateById(mesSyncRecord);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "mes同步记录-通过id删除")
	@ApiOperation(value="mes同步记录-通过id删除", notes="mes同步记录-通过id删除")
	@RequiresPermissions("admin:mes_sync_record:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		mesSyncRecordService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "mes同步记录-批量删除")
	@ApiOperation(value="mes同步记录-批量删除", notes="mes同步记录-批量删除")
	@RequiresPermissions("admin:mes_sync_record:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.mesSyncRecordService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "mes同步记录-通过id查询")
	@ApiOperation(value="mes同步记录-通过id查询", notes="mes同步记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<MesSyncRecord> queryById(@RequestParam(name="id",required=true) String id) {
		MesSyncRecord mesSyncRecord = mesSyncRecordService.getById(id);
		if(mesSyncRecord==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(mesSyncRecord);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param mesSyncRecord
    */
    @RequiresPermissions("admin:mes_sync_record:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, MesSyncRecord mesSyncRecord) {
        return super.exportXls(request, mesSyncRecord, MesSyncRecord.class, "mes同步记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:mes_sync_record:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, MesSyncRecord.class);
    }

}
