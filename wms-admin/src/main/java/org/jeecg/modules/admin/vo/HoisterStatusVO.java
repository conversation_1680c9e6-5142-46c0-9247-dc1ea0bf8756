package org.jeecg.modules.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 提升机设备状态
 * @Author: jeecg-boot
 * @Date: 2024-08-28
 * @Version: V1.0
 */
@Data
@ApiModel(value="HoisterStatusVO对象", description="提升机设备状态")
public class HoisterStatusVO {

    /**设备编号*/
    @ApiModelProperty(value = "设备编号")
    private String deviceCode;
    
    /**设备ID*/
    @ApiModelProperty(value = "设备ID")
    private Integer deviceId;
    
    /**状态*/
    @ApiModelProperty(value = "状态")
    private Integer status;
    
    /**报警信息*/
    @ApiModelProperty(value = "报警信息")
    private String alarmMsg;
    
    /**任务编号*/
    @ApiModelProperty(value = "任务编号")
    private String taskCode;
    
    /**托盘编号*/
    @ApiModelProperty(value = "托盘编号")
    private String palletCode;
    
    /**当前层*/
    @ApiModelProperty(value = "当前层")
    private Integer currentLayer;
}
