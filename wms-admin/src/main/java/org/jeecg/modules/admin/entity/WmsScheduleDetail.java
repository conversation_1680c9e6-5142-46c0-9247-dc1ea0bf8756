package org.jeecg.modules.admin.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.util.Date;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.UnsupportedEncodingException;

/**
 * @Description: 排运单明细
 * @Author: jeecg-boot
 * @Date:   2024-10-26
 * @Version: V1.0
 */
@ApiModel(value="wms_schedule_detail对象", description="排运单明细")
@Data
@TableName("wms_schedule_detail")
public class WmsScheduleDetail implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**序号*/
	@Excel(name = "序号", width = 15, type = 4)
    @ApiModelProperty(value = "序号")
    private java.lang.Integer serialNumber;
	/**K3行号*/
	@Excel(name = "K3行号", width = 15, type = 4)
    @ApiModelProperty(value = "K3行号")
    private java.lang.Integer lineNo;
	/**单据号*/
	@Excel(name = "单据号", width = 15)
    @ApiModelProperty(value = "单据号")
    private java.lang.String billNo;
	/**单据ID*/
    @ApiModelProperty(value = "单据ID")
    private java.lang.String billId;
	/**经理名称*/
	@Excel(name = "经理名称", width = 15)
    @ApiModelProperty(value = "经理名称")
    private java.lang.String managerName;
	/**品号*/
	@Excel(name = "品号", width = 15)
    @ApiModelProperty(value = "品号")
    private java.lang.String itemCode;
	/**品名*/
	@Excel(name = "品名", width = 15)
    @ApiModelProperty(value = "品名")
    private java.lang.String itemName;
	/**批号*/
	@Excel(name = "批号", width = 15)
    @ApiModelProperty(value = "批号")
    private java.lang.String batchCode;
	/**规格*/
	@Excel(name = "规格", width = 15)
    @ApiModelProperty(value = "规格")
    private java.lang.String itemSpec;
	/**单位*/
	@Excel(name = "单位", width = 15)
    @ApiModelProperty(value = "单位")
    private java.lang.String itemUnit;
	/**现存货量*/
	@Excel(name = "现存货量", width = 15, type = 4)
    @ApiModelProperty(value = "现存货量")
    private java.lang.Double inventory;
	/**计划排发数量*/
	@Excel(name = "计划排发数量", width = 15, type = 4)
    @ApiModelProperty(value = "计划排发数量")
    private java.lang.Double planQty;
	/**实际排运数量*/
	@Excel(name = "实际排运数量", width = 15, type = 4)
    @ApiModelProperty(value = "实际排运数量")
    private java.lang.Double actQty;
	/**物流公司*/
	@Excel(name = "物流公司", width = 15)
    @ApiModelProperty(value = "物流公司")
    private java.lang.String expressCompany;
	/**货运线路*/
	@Excel(name = "货运线路", width = 15)
    @ApiModelProperty(value = "货运线路")
    private java.lang.String expressWay;
	/**装车顺序*/
	@Excel(name = "装车顺序", width = 15)
    @ApiModelProperty(value = "装车顺序")
    private java.lang.String loadSequence;
	/**客户简称*/
	@Excel(name = "客户简称", width = 15)
    @ApiModelProperty(value = "客户简称")
    private java.lang.String clientName;
	/**运费简称*/
	@Excel(name = "运费简称", width = 15)
    @ApiModelProperty(value = "运费简称")
    private java.lang.String freightName;
	/**重量合计*/
	@Excel(name = "重量合计", width = 15, type = 4)
    @ApiModelProperty(value = "重量合计")
    private java.lang.Double totalWeight;
	/**体积合计*/
	@Excel(name = "体积合计", width = 15, type = 4)
    @ApiModelProperty(value = "体积合计")
    private java.lang.Double totalVolume;
	/**收货人名称*/
	@Excel(name = "收货人名称", width = 15)
    @ApiModelProperty(value = "收货人名称")
    private java.lang.String receiveBy;
	/**联系电话*/
	@Excel(name = "联系电话", width = 15)
    @ApiModelProperty(value = "联系电话")
    private java.lang.String phoneNumber;
	/**预出仓库编码*/
	@Excel(name = "预出仓库编码", width = 15)
    @ApiModelProperty(value = "预出仓库编码")
    private java.lang.String warehouseCode;
	/**预出仓库名称*/
	@Excel(name = "预出仓库名称", width = 15)
    @ApiModelProperty(value = "预出仓库名称")
    private java.lang.String warehouseName;
	/**单据明细状态*/
	@Excel(name = "单据明细状态", width = 15, dicCode = "wms_schedule_detail_bill_status")
    @ApiModelProperty(value = "单据明细状态")
    @Dict(dicCode = "wms_schedule_detail_bill_status")
    private java.lang.String lineState;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
}
