package org.jeecg.modules.admin.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.entity.WmsMaterialQuality;
import org.jeecg.modules.admin.service.IWmsMaterialQualityService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 物料质量信息表
 * @Author: jeecg-boot
 * @Date:   2024-12-26
 * @Version: V1.0
 */
@Api(tags="物料质量信息表")
@RestController
@RequestMapping("/admin/wmsMaterialQuality")
@Slf4j
public class WmsMaterialQualityController extends JeecgController<WmsMaterialQuality, IWmsMaterialQualityService> {
	@Autowired
	private IWmsMaterialQualityService wmsMaterialQualityService;
	
	/**
	 * 分页列表查询
	 *
	 * @param wmsMaterialQuality
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "物料质量信息表-分页列表查询")
	@ApiOperation(value="物料质量信息表-分页列表查询", notes="物料质量信息表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WmsMaterialQuality>> queryPageList(WmsMaterialQuality wmsMaterialQuality,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WmsMaterialQuality> queryWrapper = QueryGenerator.initQueryWrapper(wmsMaterialQuality, req.getParameterMap());
		Page<WmsMaterialQuality> page = new Page<WmsMaterialQuality>(pageNo, pageSize);
		IPage<WmsMaterialQuality> pageList = wmsMaterialQualityService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param wmsMaterialQuality
	 * @return
	 */
	@AutoLog(value = "物料质量信息表-添加")
	@ApiOperation(value="物料质量信息表-添加", notes="物料质量信息表-添加")
	@RequiresPermissions("admin:wms_material_quality:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WmsMaterialQuality wmsMaterialQuality) {
		wmsMaterialQualityService.save(wmsMaterialQuality);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param wmsMaterialQuality
	 * @return
	 */
	@AutoLog(value = "物料质量信息表-编辑")
	@ApiOperation(value="物料质量信息表-编辑", notes="物料质量信息表-编辑")
	@RequiresPermissions("admin:wms_material_quality:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WmsMaterialQuality wmsMaterialQuality) {
		wmsMaterialQualityService.updateById(wmsMaterialQuality);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "物料质量信息表-通过id删除")
	@ApiOperation(value="物料质量信息表-通过id删除", notes="物料质量信息表-通过id删除")
	@RequiresPermissions("admin:wms_material_quality:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		wmsMaterialQualityService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "物料质量信息表-批量删除")
	@ApiOperation(value="物料质量信息表-批量删除", notes="物料质量信息表-批量删除")
	@RequiresPermissions("admin:wms_material_quality:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.wmsMaterialQualityService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "物料质量信息表-通过id查询")
	@ApiOperation(value="物料质量信息表-通过id查询", notes="物料质量信息表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WmsMaterialQuality> queryById(@RequestParam(name="id",required=true) String id) {
		WmsMaterialQuality wmsMaterialQuality = wmsMaterialQualityService.getById(id);
		if(wmsMaterialQuality==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(wmsMaterialQuality);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param wmsMaterialQuality
    */
    @RequiresPermissions("admin:wms_material_quality:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WmsMaterialQuality wmsMaterialQuality) {
        return super.exportXls(request, wmsMaterialQuality, WmsMaterialQuality.class, "物料质量信息表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:wms_material_quality:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WmsMaterialQuality.class);
    }

}
