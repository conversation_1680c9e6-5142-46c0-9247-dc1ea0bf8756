package org.jeecg.modules.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description: AGV设备状态
 * @Author: jeecg-boot
 * @Date: 2024-08-28
 * @Version: V1.0
 */
@Data
@ApiModel(value="AgvStatusVO对象", description="AGV设备状态")
public class AgvStatusVO {

    /**机器人编号*/
    @ApiModelProperty(value = "机器人编号")
    private String robotCode;
    
    /**机器人方向*/
    @ApiModelProperty(value = "机器人方向")
    private String robotDir;
    
    /**机器人IP*/
    @ApiModelProperty(value = "机器人IP")
    private String robotIp;
    
    /**电池电量*/
    @ApiModelProperty(value = "电池电量")
    private String battery;
    
    /**X坐标*/
    @ApiModelProperty(value = "X坐标")
    private String posX;
    
    /**Y坐标*/
    @ApiModelProperty(value = "Y坐标")
    private String posY;
    
    /**地图编码*/
    @ApiModelProperty(value = "地图编码")
    private String mapCode;
    
    /**状态*/
    @ApiModelProperty(value = "状态")
    private String status;
    
    /**排除类型*/
    @ApiModelProperty(value = "排除类型")
    private String exclType;
    
    /**停止标志*/
    @ApiModelProperty(value = "停止标志")
    private String stop;
    
    /**货架编号*/
    @ApiModelProperty(value = "货架编号")
    private String podCode;
    
    /**货架方向*/
    @ApiModelProperty(value = "货架方向")
    private String podDir;
    
    /**路径*/
    @ApiModelProperty(value = "路径")
    private List<Object> path;
}
