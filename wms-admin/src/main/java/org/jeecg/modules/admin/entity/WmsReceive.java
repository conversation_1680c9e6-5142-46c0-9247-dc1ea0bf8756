package org.jeecg.modules.admin.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 收货主单据
 * @Author: jeecg-boot
 * @Date: 2024-06-14
 * @Version: V1.0
 */
@ApiModel(value = "wms_receive对象", description = "收货主单据")
@Data
@TableName("wms_receive")
public class WmsReceive implements Serializable {
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;

    /** 创建人 */
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;

    /** 创建日期 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;

    /** 更新人 */
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;

    /** 更新日期 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;

    /** 审核人 */
    @ApiModelProperty(value = "审核人")
    private java.lang.String checkBy;

    /** 审核日期 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核日期")
    private java.util.Date checkTime;

    /** 所属部门 */
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;

    /** 单据标识 */
    @ApiModelProperty(value = "单据标识")
    private java.lang.Integer serialNumber;

    /** 单据编号 */
    @Excel(name = "单据编号", width = 15)
    @ApiModelProperty(value = "单据编号")
    private java.lang.String billNo;

    /** 工单编号/采购单号 */
    @Excel(name = "工单编号/采购单号", width = 15)
    @ApiModelProperty(value = "工单编号/采购单号")
    private java.lang.String workNo;

    /** 单据类型 */
    @Excel(name = "单据类型", width = 15, dicCode = "receive_bill_type")
    @Dict(dicCode = "receive_bill_type")
    @ApiModelProperty(value = "单据类型")
    private java.lang.String billType;

    /** 单据名称 */
//    @Excel(name = "单据名称", width = 15)
    @ApiModelProperty(value = "单据名称")
    private java.lang.String billName;

    /** 供应商编号 */
//    @Excel(name = "供应商编号", width = 15)
    @ApiModelProperty(value = "供应商编号")
    private java.lang.String supplyCode;

    /** 供应商名称 */
//    @Excel(name = "供应商名称", width = 15)
    @ApiModelProperty(value = "供应商名称")
    private java.lang.String supplyName;

    /** 批号 */
//    @Excel(name = "批号", width = 15)
    @ApiModelProperty(value = "批号")
    private java.lang.String batchCode;

    /** 区域 */
//    @Excel(name = "区域", width = 15, dictTable = "wms_zone", dicText = "zone_name", dicCode = "zone_code")
    @Dict(dictTable = "wms_zone", dicText = "zone_name", dicCode = "zone_code")
    @ApiModelProperty(value = "区域")
    private java.lang.String zoneCode;

    /** 过账日期 */
    @Excel(name = "过账日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "过账日期")
    private java.util.Date postDate;

    /** 交货日期 */
    @Excel(name = "交货日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "交货日期")
    private java.util.Date deliveryDate;

    /** 收货日期 */
    @Excel(name = "收货日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "收货日期")
    private java.util.Date receiveDate;

    /** 收料组织名称 */
    @Excel(name = "收料组织名称", width = 15)
    @ApiModelProperty(value = "收料组织名称")
    private java.lang.String receiveOrganization;

    /** 收料部门名称 */
//    @Excel(name = "收料部门名称", width = 15)
    @ApiModelProperty(value = "收料部门名称")
    private java.lang.String receiveDepartment;

    /** 需求组织名称 */
//    @Excel(name = "需求组织名称", width = 15)
    @ApiModelProperty(value = "需求组织名称")
    private java.lang.String demandOrganization;

    /** 采购组织名称 */
//    @Excel(name = "采购组织名称", width = 15)
    @ApiModelProperty(value = "采购组织名称")
    private java.lang.String purchaseOrganization;

    /** 开始时间 */
    @Excel(name = "开始时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private java.util.Date startDate;

    /** 结束时间 */
    @Excel(name = "结束时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private java.util.Date endDate;

    /** 单据状态 */
    @Excel(name = "单据状态", width = 15, dicCode = "receive_bill_status")
    @Dict(dicCode = "receive_bill_status")
    @ApiModelProperty(value = "单据状态")
    private java.lang.String billStatus;

    /** 生产直发数量 */
    @Excel(name = "生产直发数量", width = 15)
    @ApiModelProperty(value = "生产直发数量")
    private java.lang.Double directDeliveryQty;

    /** 车牌号 */
    @Excel(name = "车牌号", width = 15)
    @ApiModelProperty(value = "车牌号")
    private java.lang.String plateNumber;

    /** 地磅系统状态 */
    @Excel(name = "地磅系统状态", width = 15,dicCode = "weight_bridge")
    @ApiModelProperty(value = "地磅系统状态")
    @Dict(dicCode = "weight_bridge")
    private java.lang.String weightBridge;

    /** 快递物流信息 */
    @Excel(name = "快递物流信息", width = 15)
    @ApiModelProperty(value = "快递物流信息")
    private java.lang.String expressInfo;

    /** 是否来自ERP */
    @Excel(name = "是否来自ERP", width = 15, dicCode = "from_erp")
    @Dict(dicCode = "from_erp")
    @ApiModelProperty(value = "是否来自ERP")
    private java.lang.String fromErp;

    /** ERP同步状态 */
    @Excel(name = "ERP同步状态", width = 15, dicCode = "erp_sync")
    @Dict(dicCode = "erp_sync")
    @ApiModelProperty(value = "ERP同步状态")
    private java.lang.String erpSync;

    /** 账套信息 */
    @Excel(name = "账套信息", width = 15)
    @ApiModelProperty(value = "账套信息")
    private java.lang.String accountCode;

    /** 货主 */
    @Excel(name = "货主", width = 15)
    @ApiModelProperty(value = "货主")
    private java.lang.String cargoOwner;

    /** 版本号 */
    @Excel(name = "版本号", width = 15)
    @ApiModelProperty(value = "版本号")
    private java.lang.String version;

    /** 质检结果 */
    @Excel(name = "质检结果", width = 15)
    @ApiModelProperty(value = "质检结果")
    private java.lang.String inspectResult;

    /** 单据备注 */
    @Excel(name = "单据备注", width = 15)
    @ApiModelProperty(value = "单据备注")
    private java.lang.String remark;
}
