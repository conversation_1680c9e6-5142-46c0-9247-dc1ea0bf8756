package org.jeecg.modules.admin.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 分布式调入单
 * @Author: jeecg-boot
 * @Date:   2024-11-02
 * @Version: V1.0
 */
@ApiModel(value="wms_distribute_in对象", description="分布式调入单")
@Data
@TableName("wms_distribute_in")
public class WmsDistributeIn implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**审核人*/
	@Excel(name = "审核人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @ApiModelProperty(value = "审核人")
    private java.lang.String checkBy;
	/**审核时间*/
	@Excel(name = "审核时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private java.util.Date checkTime;
	/**单据标识*/
	@Excel(name = "单据标识", width = 15)
    @ApiModelProperty(value = "单据标识")
    private java.lang.Integer serialNumber;
	/**调拨单号K3*/
	@Excel(name = "调拨单号K3", width = 15)
    @ApiModelProperty(value = "调拨单号K3")
    private java.lang.String workNo;
	/**调拨单号*/
	@Excel(name = "调拨单号", width = 15)
    @ApiModelProperty(value = "调拨单号")
    private java.lang.String billNo;
	/**单据类型*/
	@Excel(name = "单据类型", width = 15, dicCode = "wms_distribute_in_bill_type")
    @Dict(dicCode = "wms_distribute_in_bill_type")
    @ApiModelProperty(value = "单据类型")
    private java.lang.String billType;
	/**单据状态*/
	@Excel(name = "单据状态", width = 15, dicCode = "wms_distribute_in_bill_status")
    @Dict(dicCode = "wms_distribute_in_bill_status")
    @ApiModelProperty(value = "单据状态")
    private java.lang.String billStatus;
	/**单据名称*/
	@Excel(name = "单据名称", width = 15)
    @ApiModelProperty(value = "单据名称")
    private java.lang.String billName;
	/**调出货主名称*/
	@Excel(name = "调出货主名称", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @ApiModelProperty(value = "调出货主名称")
    private java.lang.String cargoOwnerOut;
	/**调入货主名称*/
	@Excel(name = "调入货主名称", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @ApiModelProperty(value = "调入货主名称")
    private java.lang.String cargoOwnerIn;
	/**ERP同步状态*/
	@Excel(name = "ERP同步状态", width = 15, dicCode = "erp_sync")
    @Dict(dicCode = "erp_sync")
    @ApiModelProperty(value = "ERP同步状态")
    private java.lang.String erpSync;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
}
