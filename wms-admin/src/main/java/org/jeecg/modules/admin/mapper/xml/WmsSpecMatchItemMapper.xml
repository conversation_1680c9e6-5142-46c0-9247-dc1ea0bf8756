<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.admin.mapper.WmsSpecMatchItemMapper">

    <select id="queryPageListWithJoin" resultType="org.jeecg.modules.admin.entity.WmsSpecMatchItem">
        SELECT *
        FROM (SELECT
                  m.id,
                  m.item_code,
                  m.createby,
                  m.create_time,
                  m.update_by,
                  m.update_time,
                  m.cargo_owner,
                  m.enable_flag,
                  m.single_count,
                  m.from_erp,

                  mb.item_unit,
                  mb.material_type,
                  mb.gross_weight,

                  ml.item_name,
                  ml.spec_name,
                  mq.free_flag,
                  ms.manage_method,
                  ms.min_value,
                  ms.max_value,
                  ms.warehouse_code,
                  ms.send_method
              FROM
                  wms_material m
                      LEFT JOIN
                  wms_material_base mb ON m.id = mb.id
                      LEFT JOIN
                  wms_material_l ml ON m.id = ml.id
                      LEFT JOIN
                  wms_material_quality mq ON m.id = mq.id
                      LEFT JOIN
                  wms_material_stock ms ON m.id = ms.id) n
            ${ew.customSqlSegment}
    </select>
</mapper>