package org.jeecg.modules.admin.service.impl;

import org.jeecg.modules.admin.entity.WmsOtherOut1Detail;
import org.jeecg.modules.admin.mapper.WmsOtherOut1DetailMapper;
import org.jeecg.modules.admin.service.IWmsOtherOut1DetailService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 其他出库单明细
 * @Author: jeecg-boot
 * @Date:   2024-12-21
 * @Version: V1.0
 */
@Service
public class WmsOtherOut1DetailServiceImpl extends ServiceImpl<WmsOtherOut1DetailMapper, WmsOtherOut1Detail> implements IWmsOtherOut1DetailService {
	
	@Autowired
	private WmsOtherOut1DetailMapper wmsOtherOut1DetailMapper;
	
	@Override
	public List<WmsOtherOut1Detail> selectByMainId(String mainId) {
		return wmsOtherOut1DetailMapper.selectByMainId(mainId);
	}
}
