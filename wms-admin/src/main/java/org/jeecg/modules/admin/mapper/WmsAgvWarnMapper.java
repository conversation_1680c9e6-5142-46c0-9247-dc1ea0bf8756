package org.jeecg.modules.admin.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.admin.entity.WmsAgvWarn;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: AGV告警信息
 * @Author: jeecg-boot
 * @Date:   2024-06-15
 * @Version: V1.0
 */
public interface WmsAgvWarnMapper extends BaseMapper<WmsAgvWarn> {
    
    /**
     * 查询未处理的AGV告警信息
     * @return 未处理的AGV告警信息列表
     */
    List<WmsAgvWarn> selectUnprocessedWarnings();
}
