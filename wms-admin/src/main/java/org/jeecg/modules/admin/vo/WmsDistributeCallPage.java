package org.jeecg.modules.admin.vo;

import java.util.List;
import org.jeecg.modules.admin.entity.WmsDistributeCall;
import org.jeecg.modules.admin.entity.WmsDistributeCallDetail;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelEntity;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 分布式调出单
 * @Author: jeecg-boot
 * @Date:   2024-11-05
 * @Version: V1.0
 */
@Data
@ApiModel(value="wms_distribute_callPage对象", description="分布式调出单")
public class WmsDistributeCallPage {

	/**id*/
	@ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
	@ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
	@ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**审核人*/
	@Excel(name = "审核人", width = 15)
	@ApiModelProperty(value = "审核人")
    private java.lang.String checkBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**审核日期*/
	@Excel(name = "审核日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "审核日期")
    private java.util.Date checkTime;
	/**所属部门*/
	@ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**调拨单号*/
	@Excel(name = "调拨单号", width = 15)
	@ApiModelProperty(value = "调拨单号")
    private java.lang.String billNo;
	/**调拨单号K3*/
	@Excel(name = "调拨单号K3", width = 15)
	@ApiModelProperty(value = "调拨单号K3")
    private java.lang.String workNo;
	/**单据类型*/
	@Excel(name = "单据类型", width = 15, dicCode = "distribute_out_bill_type")
    @Dict(dicCode = "distribute_out_bill_type")
	@ApiModelProperty(value = "单据类型")
    private java.lang.String billType;
	/**单据名称*/
	@Excel(name = "单据名称", width = 15)
	@ApiModelProperty(value = "单据名称")
    private java.lang.String billName;
	/**供应商编号*/
	@Excel(name = "供应商编号", width = 15)
	@ApiModelProperty(value = "供应商编号")
    private java.lang.String supplyCode;
	/**供应商名称*/
	@Excel(name = "供应商名称", width = 15)
	@ApiModelProperty(value = "供应商名称")
    private java.lang.String supplyName;
	/**批号*/
	@Excel(name = "批号", width = 15)
	@ApiModelProperty(value = "批号")
    private java.lang.String batchCode;
	/**发货区域*/
	@Excel(name = "发货区域", width = 15)
	@ApiModelProperty(value = "发货区域")
    private java.lang.String zoneCode;
	/**调拨日期*/
	@Excel(name = "调拨日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "调拨日期")
    private java.util.Date deliveryDate;
	/**开始时间*/
	@Excel(name = "开始时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "开始时间")
    private java.util.Date startDate;
	/**结束时间*/
	@Excel(name = "结束时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "结束时间")
    private java.util.Date endDate;
	/**单据状态*/
	@Excel(name = "单据状态", width = 15, dicCode = "distribute_out_bill_status")
    @Dict(dicCode = "distribute_out_bill_status")
	@ApiModelProperty(value = "单据状态")
    private java.lang.String billStatus;
	/**短运司机*/
	@Excel(name = "短运司机", width = 15)
	@ApiModelProperty(value = "短运司机")
    private java.lang.String driver;
	/**车牌号*/
	@Excel(name = "车牌号", width = 15)
	@ApiModelProperty(value = "车牌号")
    private java.lang.String plateNumber;
	/**物流公司*/
	@Excel(name = "物流公司", width = 15)
	@ApiModelProperty(value = "物流公司")
    private java.lang.String expressInfo;
	/**货运方式*/
	@Excel(name = "货运方式", width = 15, dicCode = "	wms_schedule_method")
    @Dict(dicCode = "	wms_schedule_method")
	@ApiModelProperty(value = "货运方式")
    private java.lang.String method;
	/**货运线路*/
	@Excel(name = "货运线路", width = 15)
	@ApiModelProperty(value = "货运线路")
    private java.lang.String cargoLine;
	/**是否来自ERP*/
	@Excel(name = "是否来自ERP", width = 15, dicCode = "from_erp")
    @Dict(dicCode = "from_erp")
	@ApiModelProperty(value = "是否来自ERP")
    private java.lang.String fromErp;
	/**ERP同步状态*/
	@Excel(name = "ERP同步状态", width = 15, dicCode = "	 erp_sync")
    @Dict(dicCode = "	 erp_sync")
	@ApiModelProperty(value = "ERP同步状态")
    private java.lang.String erpSync;
	/**单据备注*/
	@Excel(name = "单据备注", width = 15)
	@ApiModelProperty(value = "单据备注")
    private java.lang.String remark;

	/**排运单id*/
	@Excel(name = "排运单id", width = 15)
	@ApiModelProperty(value = "排运单id")
	private java.lang.String fid;
	/** 拍运单号 */
	@Excel(name = "拍运单号", width = 15)
	@ApiModelProperty(value = "拍运单号")
	private java.lang.String fbillno;
	/**同步信息*/
	@Excel(name = "同步信息", width = 15)
	@ApiModelProperty(value = "同步信息")
	private java.lang.String syncMessage;
	
	@ExcelCollection(name="分布式调出单明细")
	@ApiModelProperty(value = "分布式调出单明细")
	private List<WmsDistributeCallDetail> wmsDistributeCallDetailList;
	
}
