package org.jeecg.modules.admin.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.entity.WmsMaterialStock;
import org.jeecg.modules.admin.service.IWmsMaterialStockService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 物料库存表
 * @Author: jeecg-boot
 * @Date:   2024-12-26
 * @Version: V1.0
 */
@Api(tags="物料库存表")
@RestController
@RequestMapping("/admin/wmsMaterialStock")
@Slf4j
public class WmsMaterialStockController extends JeecgController<WmsMaterialStock, IWmsMaterialStockService> {
	@Autowired
	private IWmsMaterialStockService wmsMaterialStockService;
	
	/**
	 * 分页列表查询
	 *
	 * @param wmsMaterialStock
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "物料库存表-分页列表查询")
	@ApiOperation(value="物料库存表-分页列表查询", notes="物料库存表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WmsMaterialStock>> queryPageList(WmsMaterialStock wmsMaterialStock,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WmsMaterialStock> queryWrapper = QueryGenerator.initQueryWrapper(wmsMaterialStock, req.getParameterMap());
		Page<WmsMaterialStock> page = new Page<WmsMaterialStock>(pageNo, pageSize);
		IPage<WmsMaterialStock> pageList = wmsMaterialStockService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param wmsMaterialStock
	 * @return
	 */
	@AutoLog(value = "物料库存表-添加")
	@ApiOperation(value="物料库存表-添加", notes="物料库存表-添加")
	@RequiresPermissions("admin:wms_material_stock:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WmsMaterialStock wmsMaterialStock) {
		wmsMaterialStockService.save(wmsMaterialStock);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param wmsMaterialStock
	 * @return
	 */
	@AutoLog(value = "物料库存表-编辑")
	@ApiOperation(value="物料库存表-编辑", notes="物料库存表-编辑")
	@RequiresPermissions("admin:wms_material_stock:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WmsMaterialStock wmsMaterialStock) {
		wmsMaterialStockService.updateById(wmsMaterialStock);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "物料库存表-通过id删除")
	@ApiOperation(value="物料库存表-通过id删除", notes="物料库存表-通过id删除")
	@RequiresPermissions("admin:wms_material_stock:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		wmsMaterialStockService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "物料库存表-批量删除")
	@ApiOperation(value="物料库存表-批量删除", notes="物料库存表-批量删除")
	@RequiresPermissions("admin:wms_material_stock:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.wmsMaterialStockService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "物料库存表-通过id查询")
	@ApiOperation(value="物料库存表-通过id查询", notes="物料库存表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WmsMaterialStock> queryById(@RequestParam(name="id",required=true) String id) {
		WmsMaterialStock wmsMaterialStock = wmsMaterialStockService.getById(id);
		if(wmsMaterialStock==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(wmsMaterialStock);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param wmsMaterialStock
    */
    @RequiresPermissions("admin:wms_material_stock:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WmsMaterialStock wmsMaterialStock) {
        return super.exportXls(request, wmsMaterialStock, WmsMaterialStock.class, "物料库存表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:wms_material_stock:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WmsMaterialStock.class);
    }

}
