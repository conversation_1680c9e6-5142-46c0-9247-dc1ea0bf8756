package org.jeecg.modules.admin.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.common.constant.FillRuleConstant;
import org.jeecg.common.util.FillRuleUtil;
import org.jeecg.modules.admin.constant.WmsConstant;
import org.jeecg.modules.admin.entity.WmsSchedule;
import org.jeecg.modules.admin.entity.WmsScheduleDetail;
import org.jeecg.modules.admin.entity.WmsSend;
import org.jeecg.modules.admin.entity.WmsSenddetail;
import org.jeecg.modules.admin.mapper.WmsScheduleDetailMapper;
import org.jeecg.modules.admin.mapper.WmsScheduleMapper;
import org.jeecg.modules.admin.service.IWmsScheduleService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 排运单
 * @Author: jeecg-boot
 * @Date:   2024-10-26
 * @Version: V1.0
 */
@Service
public class WmsScheduleServiceImpl extends ServiceImpl<WmsScheduleMapper, WmsSchedule> implements IWmsScheduleService {

	@Autowired
	private WmsScheduleMapper wmsScheduleMapper;
	@Autowired
	private WmsScheduleDetailMapper wmsScheduleDetailMapper;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(WmsSchedule wmsSchedule, List<WmsScheduleDetail> wmsScheduleDetailList) {
		wmsScheduleMapper.insert(wmsSchedule);
		if(wmsScheduleDetailList!=null && wmsScheduleDetailList.size()>0) {
			for(WmsScheduleDetail entity:wmsScheduleDetailList) {
				//外键设置
				entity.setBillId(wmsSchedule.getId());
				wmsScheduleDetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(WmsSchedule wmsSchedule,List<WmsScheduleDetail> wmsScheduleDetailList) {
		wmsScheduleMapper.updateById(wmsSchedule);
		
		//1.先删除子表数据
		wmsScheduleDetailMapper.deleteByMainId(wmsSchedule.getId());
		
		//2.子表数据重新插入
		if(wmsScheduleDetailList!=null && wmsScheduleDetailList.size()>0) {
			for(WmsScheduleDetail entity:wmsScheduleDetailList) {
				//外键设置
				entity.setBillId(wmsSchedule.getId());
				wmsScheduleDetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		wmsScheduleDetailMapper.deleteByMainId(id);
		wmsScheduleMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			wmsScheduleDetailMapper.deleteByMainId(id.toString());
			wmsScheduleMapper.deleteById(id);
		}
	}
}
