package org.jeecg.modules.admin.mapper;

import java.util.List;
import org.jeecg.modules.admin.entity.WmsDistributeInDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 分布式调入明细
 * @Author: jeecg-boot
 * @Date:   2024-11-02
 * @Version: V1.0
 */
public interface WmsDistributeInDetailMapper extends BaseMapper<WmsDistributeInDetail> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<WmsDistributeInDetail>
   */
	public List<WmsDistributeInDetail> selectByMainId(@Param("mainId") String mainId);
}
