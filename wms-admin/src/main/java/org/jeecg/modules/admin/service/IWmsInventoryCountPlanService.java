package org.jeecg.modules.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.admin.bean.req.ReqCheckTaskDto;
import org.jeecg.modules.admin.bean.res.WmsCountTaskVo;
import org.jeecg.modules.admin.dto.WmsInventoryCountPlanDTO;
import org.jeecg.modules.admin.entity.WmsInventoryCountPlan;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.admin.entity.WmsInventoryCountTask;
import org.jeecg.modules.admin.vo.OutCheckPage;

import java.util.List;

/**
 * @Description: 盘点方案
 * @Author: jeecg-boot
 * @Date:   2024-12-19
 * @Version: V1.0
 */
public interface IWmsInventoryCountPlanService extends IService<WmsInventoryCountPlan> {

    void saveInventoryCountPlan(WmsInventoryCountPlanDTO dto);

    void updateInventoryCountPlan(WmsInventoryCountPlanDTO dto);

    IPage<WmsInventoryCountPlanDTO> queryPageListWithDetail(Page<WmsInventoryCountPlanDTO> page, QueryWrapper<WmsInventoryCountPlanDTO> queryWrapper);

    void executePlan(List<String> ids);

    void discardPlan(String id);

    List<WmsInventoryCountTask> queryWmsInventoryCountPlanByPlanId(String id);

    void delete(String id);

    void deleteBatch(List<String> ids);

    List<WmsCountTaskVo> queryPlanSumQuantity(List<String> ids);

    void closeStockCheck(List<String> asList);

    void createStocktakingTask(OutCheckPage outCheckPage);

    void replayStockCheck(List<String> ids);

    List<WmsInventoryCountTask> getCheckLoss(List<String> ids);

    void updateCheckTaskStockDetail(List<WmsInventoryCountTask> wmsInventoryCountTask);

    List<WmsCountTaskVo> getTaskSumQuantity(List<String> ids);

    void syncCheckTask(ReqCheckTaskDto reqCheckTaskDto);
}
