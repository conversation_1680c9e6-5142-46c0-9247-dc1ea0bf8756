package org.jeecg.modules.admin.vo;

import java.util.List;
import org.jeecg.modules.admin.entity.WmsProrequist;
import org.jeecg.modules.admin.entity.WmsProrequistDetail;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelEntity;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 生产单据
 * @Author: jeecg-boot
 * @Date:   2024-11-06
 * @Version: V1.0
 */
@Data
@ApiModel(value="wms_prorequistPage对象", description="生产单据")
public class WmsProrequistPage {

	/**id*/
	@ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
	@ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
	@ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**单据号*/
	@Excel(name = "单据号", width = 15)
	@ApiModelProperty(value = "单据号")
    private java.lang.String billNo;
	/**销售单号*/
	@Excel(name = "销售单号", width = 15)
	@ApiModelProperty(value = "销售单号")
    private java.lang.String workNo;
	/**单据类型*/
	@Excel(name = "单据类型", width = 15, dicCode = "wms_prorequist_bill_type")
    @Dict(dicCode = "wms_prorequist_bill_type")
	@ApiModelProperty(value = "单据类型")
    private java.lang.String billType;
	/**单据名称*/
	@Excel(name = "单据名称", width = 15)
	@ApiModelProperty(value = "单据名称")
    private java.lang.String billName;
	/**单据状态*/
	@Excel(name = "单据状态", width = 15,dicCode = "wms_prorequist_bill_status")
	@Dict(dicCode = "wms_prorequist_bill_status")
	@ApiModelProperty(value = "单据状态")
    private java.lang.String billStatus;
	/**是否来自ERP*/
	@Excel(name = "是否来自ERP", width = 15, dicCode = "from_erp")
    @Dict(dicCode = "from_erp")
	@ApiModelProperty(value = "是否来自ERP")
    private java.lang.String fromErp;
	/**ERP同步状态*/
	@Excel(name = "ERP同步状态", width = 15, dicCode = "erp_sync")
    @Dict(dicCode = "erp_sync")
	@ApiModelProperty(value = "ERP同步状态")
    private java.lang.String erpSync;
	/**日期*/
	@Excel(name = "日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "日期")
    private java.util.Date deliveryDate;
	/**生产组织*/
	@Excel(name = "生产组织", width = 15)
	@ApiModelProperty(value = "生产组织")
    private java.lang.String productOrganization;
	/**生产车间*/
	@Excel(name = "生产车间", width = 15)
	@ApiModelProperty(value = "生产车间")
    private java.lang.String workShop;
	/**生产班组*/
	@Excel(name = "生产班组", width = 15)
	@ApiModelProperty(value = "生产班组")
    private java.lang.String workGroup;
	/**生产班组编号*/
	@Excel(name = "生产班组编号", width = 15)
	@ApiModelProperty(value = "生产班组编号")
    private java.lang.String workGroupcode;
	/**审核人*/
	@Excel(name = "审核人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
	@ApiModelProperty(value = "审核人")
    private java.lang.String checkBy;
	/**审核日期*/
	@Excel(name = "审核日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "审核日期")
    private java.util.Date checkTime;
	/**单据备注*/
	@Excel(name = "单据备注", width = 15)
	@ApiModelProperty(value = "单据备注")
    private java.lang.String remark;
	/**单据备注2*/
	@Excel(name = "单据备注2", width = 15)
	@ApiModelProperty(value = "单据备注2")
    private java.lang.String remark2;
	/**同步信息*/
	@Excel(name = "同步信息", width = 15)
	@ApiModelProperty(value = "同步信息")
	private java.lang.String syncMessage;
	
	@ExcelCollection(name="生产单据明细")
	@ApiModelProperty(value = "生产单据明细")
	private List<WmsProrequistDetail> wmsProrequistDetailList;
	
}
