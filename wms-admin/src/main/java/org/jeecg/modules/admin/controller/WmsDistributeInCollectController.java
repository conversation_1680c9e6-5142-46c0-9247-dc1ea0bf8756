package org.jeecg.modules.admin.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.entity.WmsDistributeInCollect;
import org.jeecg.modules.admin.service.IWmsDistributeInCollectService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 分布式调入汇总
 * @Author: jeecg-boot
 * @Date:   2024-11-02
 * @Version: V1.0
 */
@Api(tags="分布式调入汇总")
@RestController
@RequestMapping("/admin/wmsDistributeInCollect")
@Slf4j
public class WmsDistributeInCollectController extends JeecgController<WmsDistributeInCollect, IWmsDistributeInCollectService> {
	@Autowired
	private IWmsDistributeInCollectService wmsDistributeInCollectService;
	
	/**
	 * 分页列表查询
	 *
	 * @param wmsDistributeInCollect
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "分布式调入汇总-分页列表查询")
	@ApiOperation(value="分布式调入汇总-分页列表查询", notes="分布式调入汇总-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WmsDistributeInCollect>> queryPageList(WmsDistributeInCollect wmsDistributeInCollect,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WmsDistributeInCollect> queryWrapper = QueryGenerator.initQueryWrapper(wmsDistributeInCollect, req.getParameterMap());
		Page<WmsDistributeInCollect> page = new Page<WmsDistributeInCollect>(pageNo, pageSize);
		IPage<WmsDistributeInCollect> pageList = wmsDistributeInCollectService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param wmsDistributeInCollect
	 * @return
	 */
	@AutoLog(value = "分布式调入汇总-添加")
	@ApiOperation(value="分布式调入汇总-添加", notes="分布式调入汇总-添加")
	@RequiresPermissions("admin:wms_distribute_in_collect:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WmsDistributeInCollect wmsDistributeInCollect) {
		wmsDistributeInCollectService.save(wmsDistributeInCollect);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param wmsDistributeInCollect
	 * @return
	 */
	@AutoLog(value = "分布式调入汇总-编辑")
	@ApiOperation(value="分布式调入汇总-编辑", notes="分布式调入汇总-编辑")
	@RequiresPermissions("admin:wms_distribute_in_collect:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WmsDistributeInCollect wmsDistributeInCollect) {
		wmsDistributeInCollectService.updateById(wmsDistributeInCollect);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "分布式调入汇总-通过id删除")
	@ApiOperation(value="分布式调入汇总-通过id删除", notes="分布式调入汇总-通过id删除")
	@RequiresPermissions("admin:wms_distribute_in_collect:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		wmsDistributeInCollectService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "分布式调入汇总-批量删除")
	@ApiOperation(value="分布式调入汇总-批量删除", notes="分布式调入汇总-批量删除")
	@RequiresPermissions("admin:wms_distribute_in_collect:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.wmsDistributeInCollectService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "分布式调入汇总-通过id查询")
	@ApiOperation(value="分布式调入汇总-通过id查询", notes="分布式调入汇总-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WmsDistributeInCollect> queryById(@RequestParam(name="id",required=true) String id) {
		WmsDistributeInCollect wmsDistributeInCollect = wmsDistributeInCollectService.getById(id);
		if(wmsDistributeInCollect==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(wmsDistributeInCollect);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param wmsDistributeInCollect
    */
    @RequiresPermissions("admin:wms_distribute_in_collect:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WmsDistributeInCollect wmsDistributeInCollect) {
        return super.exportXls(request, wmsDistributeInCollect, WmsDistributeInCollect.class, "分布式调入汇总");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:wms_distribute_in_collect:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WmsDistributeInCollect.class);
    }

}
