package org.jeecg.modules.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.admin.entity.WmsTaskQueueAgv;
import org.jeecg.modules.admin.mapper.WmsTaskQueueAgvMapper;
import org.jeecg.modules.admin.service.IWmsTaskQueueAgvService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: AGV任务
 * @Author: jeecg-boot
 * @Date:   2024-08-20
 * @Version: V1.0
 */
@Service
public class WmsTaskQueueAgvServiceImpl extends ServiceImpl<WmsTaskQueueAgvMapper, WmsTaskQueueAgv> implements IWmsTaskQueueAgvService {
    @Autowired
    private WmsTaskQueueAgvMapper wmsTaskQueueAgvMapper;
    
    @Override
    public IPage<WmsTaskQueueAgv> pageList(Page<WmsTaskQueueAgv> page, QueryWrapper<WmsTaskQueueAgv> queryWrapper) {
        return wmsTaskQueueAgvMapper.page(page, queryWrapper);
    }
    
    @Override
    public Map<String, Object> getKNBHAgvTaskCompletionRate() {
        Map<String, Object> resultMap = new HashMap<>();
        
        // 查询当天的所有库内备货AGV任务（taskType为18）
        QueryWrapper<WmsTaskQueueAgv> totalTaskQuery = new QueryWrapper<>();
        totalTaskQuery.eq("task_type", "18")
                     .apply("DATE(create_time) = CURDATE()");
        
        // 使用page方法查询，因为它会同时查询当前任务表和历史任务表
        Page<WmsTaskQueueAgv> page = new Page<>(1, Integer.MAX_VALUE);
        IPage<WmsTaskQueueAgv> pageResult = wmsTaskQueueAgvMapper.page(page, totalTaskQuery);
        List<WmsTaskQueueAgv> allTasks = pageResult.getRecords();
        
        // 总任务数量
        int totalTaskCount = allTasks.size();
        
        // 已完成任务数量（taskState为4的任务）
        int completedTaskCount = 0;
        for (WmsTaskQueueAgv task : allTasks) {
            if ("4".equals(task.getTaskState())) {
                completedTaskCount++;
            }
        }
        
        // 计算完成率
        double completionRate = 0.0;
        if (totalTaskCount > 0) {
            completionRate = (double) completedTaskCount / totalTaskCount * 100;
        }
        // 设置结果
        resultMap.put("completionRate", String.format("%.2f", completionRate) + "%");
        resultMap.put("totalTaskCount", totalTaskCount);
        resultMap.put("completedTaskCount", completedTaskCount);
        
        return resultMap;
    }
}
