package org.jeecg.modules.admin.service;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.modules.admin.entity.WmsDistributeInDetail;
import org.jeecg.modules.admin.entity.WmsDistributeIn;
import com.baomidou.mybatisplus.extension.service.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 分布式调入单
 * @Author: jeecg-boot
 * @Date:   2024-11-02
 * @Version: V1.0
 */
public interface IWmsDistributeInService extends IService<WmsDistributeIn> {

	/**
	 * 添加一对多
	 *
	 * @param wmsDistributeIn
	 * @param wmsDistributeInDetailList
	 */
	public void saveMain(WmsDistributeIn wmsDistributeIn,List<WmsDistributeInDetail> wmsDistributeInDetailList) ;
	
	/**
	 * 修改一对多
	 *
	 * @param wmsDistributeIn
	 * @param wmsDistributeInDetailList
	 */
	public void updateMain(WmsDistributeIn wmsDistributeIn,List<WmsDistributeInDetail> wmsDistributeInDetailList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

    JSONObject submitRecord(String id, boolean flag);
}
