package org.jeecg.modules.admin.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.log4j.Log4j2;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jeecg.common.api.dto.LogDTO;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.admin.entity.MesInterface;
import org.jeecg.modules.admin.entity.WmsDistributeIn;
import org.jeecg.modules.admin.entity.WmsDistributeInDetail;
import org.jeecg.modules.admin.mapper.WmsDistributeInDetailMapper;
import org.jeecg.modules.admin.mapper.WmsDistributeInMapper;
import org.jeecg.modules.admin.service.IMesInterfaceService;
import org.jeecg.modules.admin.service.IWmsDistributeInService;
import org.jeecg.modules.base.service.BaseCommonService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.apache.shiro.SecurityUtils;
import org.jeecg.modules.admin.util.HttpClientUtil;

import java.io.IOException;
import java.io.Serializable;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 分布式调入单
 * @Author: jeecg-boot
 * @Date:   2024-11-02
 * @Version: V1.0
 */
@Service
@Log4j2
public class WmsDistributeInServiceImpl extends ServiceImpl<WmsDistributeInMapper, WmsDistributeIn> implements IWmsDistributeInService {

	@Autowired
	private WmsDistributeInMapper wmsDistributeInMapper;
	@Autowired
	private WmsDistributeInDetailMapper wmsDistributeInDetailMapper;
	@Autowired
	private IMesInterfaceService mesInterfaceService;
	@Autowired
	private StringRedisTemplate redisTemplate;
	@Autowired
	private BaseCommonService baseCommonService;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(WmsDistributeIn wmsDistributeIn, List<WmsDistributeInDetail> wmsDistributeInDetailList) {
		wmsDistributeInMapper.insert(wmsDistributeIn);
		if(wmsDistributeInDetailList!=null && wmsDistributeInDetailList.size()>0) {
			for(WmsDistributeInDetail entity:wmsDistributeInDetailList) {
				//外键设置
				entity.setBillId(wmsDistributeIn.getId());
				wmsDistributeInDetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(WmsDistributeIn wmsDistributeIn,List<WmsDistributeInDetail> wmsDistributeInDetailList) {
		wmsDistributeInMapper.updateById(wmsDistributeIn);
		
		//1.先删除子表数据
		wmsDistributeInDetailMapper.deleteByMainId(wmsDistributeIn.getId());
		
		//2.子表数据重新插入
		if(wmsDistributeInDetailList!=null && wmsDistributeInDetailList.size()>0) {
			for(WmsDistributeInDetail entity:wmsDistributeInDetailList) {
				//外键设置
				entity.setBillId(wmsDistributeIn.getId());
				wmsDistributeInDetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		wmsDistributeInDetailMapper.deleteByMainId(id);
		wmsDistributeInMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			wmsDistributeInDetailMapper.deleteByMainId(id.toString());
			wmsDistributeInMapper.deleteById(id);
		}
	}

	@Override
	public JSONObject submitRecord(String id, boolean ignoreInterationFlag) {
		log.info("开始提交分布式调入单数据，id:{}", id);

		// 1. 根据id查询主表
		WmsDistributeIn wmsDistributeIn = wmsDistributeInMapper.selectById(id);
		if (wmsDistributeIn == null) {
			throw new RuntimeException("分布式调入单不存在，id:" + id);
		}

		// 2. 校验状态，判断是否允许提交
		//   这里仅做示例判断，实际业务中可能是 "4" 表示已完成、可同步
		if (!"4".equals(wmsDistributeIn.getBillStatus())) {
			throw new RuntimeException("分布式调入单非完成状态，无法提交！");
		}

		// 3. 判断是否已同步
		//   假设 "2" 表示已同步，"3" 表示同步失败
		if ("2".equals(wmsDistributeIn.getErpSync())) {
			throw new RuntimeException("分布式调入单已同步，id:" + id);
		}

		// 4. 获取接口配置：JK026
		MesInterface mi = mesInterfaceService.getOne(new QueryWrapper<MesInterface>()
				.eq("interface_code", "JK026"));
		if (mi == null) {
			throw new RuntimeException("接口配置不存在: JK026");
		}
		// 4.1 校验启用状态
		if (!"1".equals(mi.getInterfaceStatus())) {
			throw new RuntimeException("接口JK026未启用！");
		}
		// 4.2 请求方式校验
		if (!"POST".equalsIgnoreCase(mi.getRequestMethod())) {
			throw new RuntimeException("接口JK026的请求方式不是POST！");
		}

		// 5. 获取 MES Token
		String accessToken = redisTemplate.opsForValue().get("mes_login_access_token");
		if (accessToken == null) {
			// 如果没有获取到MES登录token，则调用performLoginAndStoreToken方法获取
			try {
				log.info("未获取到MES登录token，正在调用登录接口获取...");
				mesInterfaceService.performLoginAndStoreToken();
				// 重新获取token
				accessToken = redisTemplate.opsForValue().get("mes_login_access_token");
				if (accessToken == null) {
					throw new RuntimeException("登录接口调用成功但仍未获取到MES登录token！");
				}
				log.info("已成功获取MES登录token");
			} catch (IOException e) {
				throw new RuntimeException("调用MES登录接口时发生异常: " + e.getMessage(), e);
			}
		}

		// 6. 构建请求参数
		JSONObject requestParam = buildRequestParam(wmsDistributeIn, ignoreInterationFlag);

		// 7. 发起接口请求
		JSONObject responseJson = doPost(mi.getInterfaceUrl(), accessToken, requestParam);

		// 8. 处理返回
		return handleResponse(responseJson, wmsDistributeIn);
	}

	/**
	 * 构建分布式调入单回传K3的请求参数
	 */
	private JSONObject buildRequestParam(WmsDistributeIn wmsDistributeIn, boolean ignoreInterationFlag) {
		//获取当前登录用户
		LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		JSONObject requestParam = new JSONObject();

		// 1) date 日期 (格式：yyyy-MM-dd)，这里假设没有"调入日期"字段，就用审核时间或创建时间代替
		String dateStr = "";
		if (wmsDistributeIn.getCheckTime() != null) {
			dateStr = new SimpleDateFormat("yyyy-MM-dd").format(wmsDistributeIn.getCheckTime());
		} else if (wmsDistributeIn.getCreateTime() != null) {
			dateStr = new SimpleDateFormat("yyyy-MM-dd").format(wmsDistributeIn.getCreateTime());
		}
		requestParam.put("date", dateStr);

		// 2) 创建人 & 审核人（可根据需要自行获取）
		requestParam.put("creator", loginUser.getUsername());
		requestParam.put("auditor", wmsDistributeIn.getCheckBy() == null ? "" : wmsDistributeIn.getCheckBy());

		// 3) 是否忽略审核
		requestParam.put("ignoreInterationFlag", ignoreInterationFlag);

		// 4) fbilltypenumber、ftransferbiztype、ftransferdirect、fthcustnumber、fstockergroupnumber、fnote
		//   这些可根据业务取值。示例中写死/或从 wmsDistributeIn 的字段获取
		requestParam.put("fbilltypenumber", "A");
		requestParam.put("ftransferbiztype", "A");
		requestParam.put("ftransferdirect", "A");
		requestParam.put("fthcustnumber", "");         // 若没有可空
		requestParam.put("fstockergroupnumber", "");   // 若没有可空
		requestParam.put("fnote", wmsDistributeIn.getRemark() == null ? "" : wmsDistributeIn.getRemark());

		// 5) list 明细
		JSONArray detailList = new JSONArray();
		List<WmsDistributeInDetail> details = wmsDistributeInDetailMapper.selectList(
				new QueryWrapper<WmsDistributeInDetail>().eq("bill_id", wmsDistributeIn.getId())
		);
		// 构建明细
		for (WmsDistributeInDetail detail : details) {
			JSONObject item = new JSONObject();
			// fsrcmaterialnumber 调出物料编码
			//   假设 detail.getItemCode() 就是调出的物料编码
			item.put("fsrcmaterialnumber", detail.getItemCode());
			// materialnumber 调入物料编码
			//   如果调出物料编码与调入物料编码相同，也可以直接赋值
			//   或者项目中有"调入物料编码"字段
			item.put("materialnumber", detail.getItemCode());

			// fownernumber 调入货主
			item.put("fownernumber", wmsDistributeIn.getCargoOwnerIn() == null ? "" : wmsDistributeIn.getCargoOwnerIn());
			// fowneroutnumber 调出货主
			item.put("fowneroutnumber", wmsDistributeIn.getCargoOwnerOut() == null ? "" : wmsDistributeIn.getCargoOwnerOut());

			// fsrcstocknumber 调出库存(仓库)
			//   如果有存储在 detail.getXXXX() 中，请根据实际字段替换
//			item.put("fsrcstocknumber", "011101");  // 示例写死

			// fdeststocknumber 调入仓库
			item.put("fdeststocknumber", detail.getWarehouseIn() == null ? "" : detail.getWarehouseIn());

			// fqty + fbaseqty, 如接口所述，二者加起来 > 0
			double realQty = detail.getReceiveQty() == null ? 0 : detail.getReceiveQty();
			item.put("fqty", realQty);
			item.put("fbaseqty", realQty);

			// 其他可选字段：fsecqty、fpathlossqty、fplantransferqty、linkList...
			// 这里根据接口文档和业务实际需要补充
			item.put("fsecqty", 0);
			item.put("fpathlossqty", 0);
			item.put("fplantransferqty", realQty); // 假设计划调出数量 = 实际数量
			// linkList
			JSONArray linkList = new JSONArray();
			// 这里仅做示例，假如无上游单据，则可不传
			// JSONObject linkItem = new JSONObject();
			// linkItem.put("fbaseunitqtyold", realQty);
			// linkItem.put("fbaseqty", realQty);
			// linkItem.put("fsid", "上游单据ID");
			// linkItem.put("fsbillid", "上游单据明细ID");
			// linkList.add(linkItem);
			item.put("linkList", linkList);

			detailList.add(item);
		}
		requestParam.put("list", detailList);

		return requestParam;
	}

	/**
	 * 处理接口返回
	 */
	private JSONObject handleResponse(JSONObject responseJson, WmsDistributeIn wmsDistributeIn) {
		// 返回示例:
		// {
		//   "isSuccess": true,
		//   "errors": [],
		//   "errorCode": 0,
		//   "successEntitys": [
		//     {
		//       "id": 539838,
		//       "number": "FBDR24000039",
		//       "dindex": 0
		//     }
		//   ],
		//   "msgCode": 0,
		//   "successMessages": []
		// }

		JSONObject result = new JSONObject();
		boolean isSuccess = responseJson.getBooleanValue("isSuccess");
		int msgCode = responseJson.getIntValue("msgCode");
		JSONArray errors = responseJson.getJSONArray("errors");

		if (isSuccess) {
			// 获取调拨单号
			JSONArray successEntitys = responseJson.getJSONArray("successEntitys");
			String k3Number = "";
			if (successEntitys != null && !successEntitys.isEmpty()) {
				k3Number = successEntitys.getJSONObject(0).getString("number");
			}
			// 更新数据库记录
			wmsDistributeIn.setWorkNo(k3Number);
			wmsDistributeIn.setErpSync("2"); // 2表示已同步
			wmsDistributeInMapper.updateById(wmsDistributeIn);

			log.info("分布式调入单同步成功, ID: {}, K3单号: {}", wmsDistributeIn.getId(), k3Number);
			result.put("success", true);
			result.put("message", "分布式调入单同步成功");
			result.put("k3Number", k3Number);
		} else {
			// msgCode = 11 表示可忽略的异常
			if (msgCode == 11) {
				result.put("success", false);
				result.put("message", "更新库存时出现可忽略的异常数据，是否强制提交？");
				result.put("forceSubmit", true);
			} else {
				String errorMessage = (errors != null && !errors.isEmpty())
						? errors.toJSONString()
						: responseJson.toJSONString();
				log.error("分布式调入单同步失败, ID: {}, 错误信息: {}", wmsDistributeIn.getId(), errorMessage);
				result.put("success", false);
				result.put("message", "分布式调入单同步失败: " + errorMessage);
			}
			// 更新同步状态为失败
			wmsDistributeIn.setErpSync("3");
			wmsDistributeInMapper.updateById(wmsDistributeIn);
		}
		return result;
	}

	/**
	 * 通用 POST 请求
	 */
	private JSONObject doPost(String url, String token, JSONObject requestBody) {
		return HttpClientUtil.doPostEnhanced(url, token, requestBody, baseCommonService, "MES分布式调入", "syncDistributeInData");
	}

	/**
	 * 获取本机 IP 地址
	 */
	private String getLocalIpAddress() {
		try {
			InetAddress localHost = InetAddress.getLocalHost();
			return localHost.getHostAddress();
		} catch (UnknownHostException e) {
			return "未知";
		}
	}
}
