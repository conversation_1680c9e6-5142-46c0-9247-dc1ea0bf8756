package org.jeecg.modules.admin.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.entity.WmsWarehouse;
import org.jeecg.modules.admin.entity.WmsZone;
import org.jeecg.modules.admin.service.IWmsWarehouseService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 仓库表
 * @Author: jeecg-boot
 * @Date: 2024-06-13
 * @Version: V1.0
 */
@Api(tags = "仓库表")
@RestController
@RequestMapping("/admin/wmsWarehouse")
@Slf4j
public class WmsWarehouseController extends JeecgController<WmsWarehouse, IWmsWarehouseService> {
    @Autowired
    private IWmsWarehouseService wmsWarehouseService;

    /**
     * 分页列表查询
     *
     * @param wmsWarehouse
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "仓库表-分页列表查询")
    @ApiOperation(value = "仓库表-分页列表查询", notes = "仓库表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<WmsWarehouse>> queryPageList(WmsWarehouse wmsWarehouse,
                                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                     HttpServletRequest req) {
        QueryWrapper<WmsWarehouse> queryWrapper = QueryGenerator.initQueryWrapper(wmsWarehouse, req.getParameterMap());
        Page<WmsWarehouse> page = new Page<WmsWarehouse>(pageNo, pageSize);
        IPage<WmsWarehouse> pageList = wmsWarehouseService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param wmsWarehouse
     * @return
     */
    @AutoLog(value = "仓库表-添加")
    @ApiOperation(value = "仓库表-添加", notes = "仓库表-添加")
    @RequiresPermissions("admin:wms_warehouse:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody WmsWarehouse wmsWarehouse) {
        wmsWarehouseService.save(wmsWarehouse);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param wmsWarehouse
     * @return
     */
    @AutoLog(value = "仓库表-编辑")
    @ApiOperation(value = "仓库表-编辑", notes = "仓库表-编辑")
    @RequiresPermissions("admin:wms_warehouse:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody WmsWarehouse wmsWarehouse) {
        wmsWarehouseService.updateById(wmsWarehouse);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "仓库表-通过id删除")
    @ApiOperation(value = "仓库表-通过id删除", notes = "仓库表-通过id删除")
    @RequiresPermissions("admin:wms_warehouse:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        wmsWarehouseService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "仓库表-批量删除")
    @ApiOperation(value = "仓库表-批量删除", notes = "仓库表-批量删除")
    @RequiresPermissions("admin:wms_warehouse:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.wmsWarehouseService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "仓库表-通过id查询")
    @ApiOperation(value = "仓库表-通过id查询", notes = "仓库表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<WmsWarehouse> queryById(@RequestParam(name = "id", required = true) String id) {
        WmsWarehouse wmsWarehouse = wmsWarehouseService.getById(id);
        if (wmsWarehouse == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(wmsWarehouse);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param wmsWarehouse
     */
    @RequiresPermissions("admin:wms_warehouse:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WmsWarehouse wmsWarehouse) {
        return super.exportXls(request, wmsWarehouse, WmsWarehouse.class, "仓库表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("admin:wms_warehouse:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WmsWarehouse.class);
    }

    /**
     * 查询仓库以及下面的区域，并以JSON格式返回
     *
     * @return
     */
    @AutoLog(value = "仓库表-查询仓库以及下面的区域-移动端")
    @ApiOperation(value = "仓库表-查询仓库以及下面的区域-移动端", notes = "仓库表-查询仓库以及下面的区域-移动端")
    @GetMapping(value = "/queryWarehouseArea")
    public Result<JSONArray> queryWarehouseArea(@RequestParam(name = "type", required = false) String type) {
        JSONArray jsonArray = wmsWarehouseService.queryWarehouseArea(type);
        return Result.OK(jsonArray);
    }
    /**
     * 查询仓库以及下面的区域，并以JSON格式返回
     * @return
     */
    @ApiOperation(value="仓库表-查询仓库以及下面的区域-PC端", notes="仓库表-查询仓库以及下面的区域-PC端")
    @GetMapping(value = "/queryWarehouseAreaPC")
    public Result<JSONArray> queryWarehouseAreaPC() {
        JSONArray jsonArray = wmsWarehouseService.queryWarehouseAreaPC();
        return Result.OK(jsonArray);
    }

    /**
     * 查询仓库类型为XBK仓库下的区域
     * @return
     */
    @ApiOperation(value="仓库表-查询仓库类型为XBK仓库下的区域", notes="仓库表-查询仓库类型为XBK仓库下的区域")
    @GetMapping(value = "/queryXBKWarehouseArea")
    public Result<List<WmsZone>> queryXBKWarehouseArea() {
        List<WmsZone> wmsZones = wmsWarehouseService.queryXBKWarehouseArea();
        return Result.OK(wmsZones);
    }
}
