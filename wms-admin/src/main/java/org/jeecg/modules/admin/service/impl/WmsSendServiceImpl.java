package org.jeecg.modules.admin.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.dto.LogDTO;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.constant.FillRuleConstant;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.FillRuleUtil;
import org.jeecg.modules.admin.constant.WmsConstant;
import org.jeecg.modules.admin.entity.*;
import org.jeecg.modules.admin.entity.WmsSenddetail;
import org.jeecg.modules.admin.mapper.WmsDistributeCallMapper;
import org.jeecg.modules.admin.mapper.WmsSenddetailMapper;
import org.jeecg.modules.admin.mapper.WmsSendMapper;
import org.jeecg.modules.admin.service.IMesInterfaceService;
import org.jeecg.modules.admin.service.IWmsDistributeCallDetailService;
import org.jeecg.modules.admin.service.IWmsDistributeCallService;
import org.jeecg.modules.admin.service.IWmsSendService;
import org.jeecg.modules.admin.vo.WmsInAndOutStatisticsPage;
import org.jeecg.modules.base.service.BaseCommonService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.jeecg.modules.admin.util.HttpClientUtil;

import java.io.IOException;
import java.io.Serializable;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * @Description: 出库主单据
 * @Author: jeecg-boot
 * @Date:   2024-06-19
 * @Version: V1.0
 */
@Slf4j
@Service
public class WmsSendServiceImpl extends ServiceImpl<WmsSendMapper, WmsSend> implements IWmsSendService {

	@Autowired
	private WmsSendMapper wmsSendMapper;
	@Autowired
	private WmsSenddetailMapper wmsSenddetailMapper;
	@Autowired
	private IWmsDistributeCallService wmsDistributeCallService;
	@Autowired
	private IWmsDistributeCallDetailService wmsDistributeCallDetailService;
	@Autowired
	private IMesInterfaceService mesInterfaceService;
	@Autowired
	private StringRedisTemplate redisTemplate;
	@Autowired
	private BaseCommonService baseCommonService;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(WmsSend wmsSend, List<WmsSenddetail> wmsSenddetailList) {
		wmsSendMapper.insert(wmsSend);
		if(wmsSenddetailList!=null && wmsSenddetailList.size()>0) {
			for(WmsSenddetail entity:wmsSenddetailList) {
				//外键设置
				entity.setBillId(wmsSend.getId());
				wmsSenddetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(WmsSend wmsSend,List<WmsSenddetail> wmsSenddetailList) {
		wmsSendMapper.updateById(wmsSend);
		
		//1.先删除子表数据
		wmsSenddetailMapper.deleteByMainId(wmsSend.getId());
		
		//2.子表数据重新插入
		if(wmsSenddetailList!=null && wmsSenddetailList.size()>0) {
			for(WmsSenddetail entity:wmsSenddetailList) {
				//外键设置
				entity.setBillId(wmsSend.getId());
				wmsSenddetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		wmsSenddetailMapper.deleteByMainId(id);
		wmsSendMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			wmsSenddetailMapper.deleteByMainId(id.toString());
			wmsSendMapper.deleteById(id);
		}
	}

	@Override
	public Map<String, Object> getWeekSend(String startTime, String endTime) {
		// 解析开始时间和结束时间
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		LocalDate startDate = LocalDate.parse(startTime, formatter);
		LocalDate endDate = LocalDate.parse(endTime, formatter);

		// 获取指定时间范围内的发送单据明细
		QueryWrapper<WmsSenddetail> queryWrapper = new QueryWrapper<>();
		queryWrapper.between("DATE(create_time)", startDate, endDate);
		List<WmsSenddetail> sendList = wmsSenddetailMapper.selectList(queryWrapper);

		// 统计时间范围内每天的发送数量
		Map<String, Double> dailyTotals = sendList.stream()
				.collect(Collectors.groupingBy(
						send -> send.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(formatter),
						Collectors.summingDouble(WmsSenddetail::getActQty)
				));

		// 获取时间范围内的日期列表
		long numOfDays = endDate.toEpochDay() - startDate.toEpochDay() + 1;
		List<String> dateList = Stream.iterate(startDate, date -> date.plusDays(1))
				.limit(numOfDays)
				.map(date -> date.format(formatter))
				.collect(Collectors.toList());

		// 发送数量列表
		List<String> numList = dateList.stream()
				.map(date -> dailyTotals.getOrDefault(date, 0.0).toString())
				.collect(Collectors.toList());

		// 构建结果 Map
		Map<String, Object> result = new HashMap<>();
		result.put("Num", numList);
		result.put("dayList", dateList);

		return result;
	}

	@Override
	public Map<String, Object> getDaySend(String date) {
		// 初始化每小时的出库数量为0
		Map<String, Double> hourlyTotals = IntStream.range(0, 24)
				.boxed()
				.collect(Collectors.toMap(hour -> String.format("%02d:00", hour), hour -> 0.0));

		// 获取指定日期的发送单据明细
		QueryWrapper<WmsSenddetail> queryWrapper = new QueryWrapper<>();
		queryWrapper.apply("DATE(create_time) = {0}", date);
		List<WmsSenddetail> sendList = wmsSenddetailMapper.selectList(queryWrapper);

		// 统计每小时的发送数量
		sendList.forEach(send -> {
			String hour = String.format("%02d:00", send.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).getHour());
			hourlyTotals.put(hour, hourlyTotals.get(hour) + send.getActQty());
		});

		// 获取小时列表
		List<String> hourList = IntStream.range(0, 24)
				.boxed()
				.map(hour -> String.format("%02d:00", hour))
				.collect(Collectors.toList());

		// 获取数量列表
		List<String> numList = hourList.stream()
				.map(hour -> hourlyTotals.getOrDefault(hour, 0.0).toString())
				.collect(Collectors.toList());

		// 构建结果 Map
		Map<String, Object> result = new HashMap<>();
		result.put("Num", numList);
		result.put("hourList", hourList);

		return result;
	}


	@Override
	public List<WmsInAndOutStatisticsPage> getSendReceive(String startTime, String endTime, String itemNumber, String itemName) {
		return wmsSendMapper.getSendReceive(startTime, endTime, itemNumber, itemName);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void receiveSalesOut(JSONArray dataArr) {
		if (dataArr == null || dataArr.isEmpty()) {
			throw new RuntimeException("请求参数中未包含 data 数组或数据为空");
		}
		try {
			// 按fbillno分组，记录每个fbillno对应的billNo
			Map<String, String> billNoMap = new HashMap<>();

			for (int i = 0; i < dataArr.size(); i++) {
				JSONObject obj = dataArr.getJSONObject(i);
				String sellerName = obj.getString("fselleridname");
				String fbillno = obj.getString("fbillno");

				// 检查该fbillno是否已有对应的billNo
				String billNo = billNoMap.get(fbillno);

				if ("待查市场经理".equals(sellerName)) {
					// 处理分布式调出单
					if (billNo == null) {
						// 只有当该fbillno还没有对应billNo时才生成新的
						QueryWrapper<WmsDistributeCall> queryWrapper = new QueryWrapper<>();
						queryWrapper.eq("fbillno", fbillno);
						List<WmsDistributeCall> list = wmsDistributeCallService.list(queryWrapper);
						if (list != null && !list.isEmpty()) {
							// 使用已存在记录的billNo
							billNo = list.get(0).getBillNo();
						} else {
							// 生成新的billNo
							billNo = (String) FillRuleUtil.executeRule(FillRuleConstant.DISTRIBUTE_OUT_ORDER, new JSONObject());
						}
						// 记录该fbillno对应的billNo
						billNoMap.put(fbillno, billNo);
					}

					WmsDistributeCall distributeCall = new WmsDistributeCall();
					distributeCall.setBillNo(billNo); // 使用一致的billNo
					distributeCall.setFbillno(obj.getString("fbillno"));
					distributeCall.setFid(obj.getString("fid"));
					distributeCall.setWorkNo(obj.getString("fsrcbillno"));
					distributeCall.setBillType("FBCD");
					distributeCall.setBillName("分布式调出单");
					distributeCall.setBillStatus("1");
					distributeCall.setFromErp("1");
					distributeCall.setErpSync("0");

					String fdateStr = obj.getString("fdate");
					if (fdateStr != null) {
						distributeCall.setDeliveryDate(new SimpleDateFormat("yyyy-MM-dd").parse(fdateStr));
					}

					distributeCall.setZoneCode(obj.getString("fstockorgid"));
					distributeCall.setDriver(obj.getString("fbijdysj"));
					distributeCall.setPlateNumber(obj.getString("fbijcarnumber"));
					distributeCall.setExpressInfo(obj.getString("flogcomname"));
					distributeCall.setMethod(obj.getString("hyfsdatavalue"));
					distributeCall.setRemark(obj.getString("fentrynote"));
					distributeCall.setCargoLine(obj.getString("fbijhylx"));
					distributeCall.setCheckBy(obj.getString("fapproveridname"));

					String checkTime = obj.getString("fapprovedate");
					if (checkTime != null) {
						distributeCall.setCheckTime(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss").parse(checkTime));
					}else {
						distributeCall.setCheckTime(new Date());
					}

					List<WmsDistributeCallDetail> distributeCallDetailList = new ArrayList<>();
					WmsDistributeCallDetail distributeCallDetail = new WmsDistributeCallDetail();
					distributeCallDetail.setLineNo(obj.getInteger("fseq"));
					distributeCallDetail.setWorkNo(billNo); // 使用一致的billNo
					distributeCallDetail.setBillNo(billNo); // 使用一致的billNo
					distributeCallDetail.setItemCode(obj.getString("fmaterialidnumber"));
					distributeCallDetail.setItemName(obj.getString("fmaterialidname"));
					distributeCallDetail.setPlanQty(obj.getDouble("fmustqty"));
					distributeCallDetail.setSingleWeight(obj.getDouble("fbijgrossweight"));
					distributeCallDetail.setSingleVolume(obj.getDouble("fbijvolume"));
					distributeCallDetail.setTareWeight(obj.getDouble("fbijpackweight"));
					distributeCallDetail.setGrossWeight(obj.getDouble("fbijallgrossweight"));
					distributeCallDetail.setVolume(obj.getDouble("fbijallvolume"));
					distributeCallDetail.setRemark(obj.getString("fentrynote"));
					distributeCallDetail.setReceiveBy(obj.getString("freceivername"));
					distributeCallDetail.setFentryid(obj.getString("fentryid"));
					distributeCallDetail.setWarehouseOut(obj.getString("fstockidnumber"));
					distributeCallDetail.setDisQty(0.0);
					distributeCallDetail.setActQty(0.0);
					distributeCallDetail.setLineState("1");

					QueryWrapper<WmsDistributeCall> queryWrapper = new QueryWrapper<>();
					queryWrapper.eq("fbillno", distributeCall.getFbillno());
					List<WmsDistributeCall> list = wmsDistributeCallService.list(queryWrapper);
					if (list != null && !list.isEmpty()) {
						// 获取当前主单据ID
						String mainId = list.get(0).getId();
						
						// 获取已经存在的明细数据
						List<WmsDistributeCallDetail> existingDetails = wmsDistributeCallDetailService.selectByMainId(mainId);
						
						// 检查是否存在重复记录（行号+物料编号）
						boolean isDuplicate = false;
						if(existingDetails != null && !existingDetails.isEmpty()) {
							isDuplicate = existingDetails.stream()
									.anyMatch(existingDetail -> 
											existingDetail.getLineNo() != null && 
											existingDetail.getLineNo().equals(distributeCallDetail.getLineNo()) && 
											existingDetail.getItemCode() != null && 
											existingDetail.getItemCode().equals(distributeCallDetail.getItemCode())
									);
						}
						
						// 如果不是重复记录，才进行插入
						if (!isDuplicate) {
							distributeCallDetail.setWorkId(mainId);
							distributeCallDetail.setBillId(mainId);
							wmsDistributeCallDetailService.save(distributeCallDetail);
						} else {
							log.info("跳过重复的分布式调出单明细记录：行号 [{}]，物料编号 [{}]", distributeCallDetail.getLineNo(), distributeCallDetail.getItemCode());
						}
					}else {
						distributeCallDetailList.add(distributeCallDetail);
						wmsDistributeCallService.saveMain(distributeCall, distributeCallDetailList);
					}
				} else {
					// 处理销售出库单
					if (billNo == null) {
						// 只有当该fbillno还没有对应billNo时才检查或生成新的
						QueryWrapper<WmsSend> queryWrapper = new QueryWrapper<>();
						queryWrapper.eq("fbillno", fbillno);
						queryWrapper.orderByDesc("create_time");
						List<WmsSend> list = super.list(queryWrapper);
						if (list != null && !list.isEmpty() && list.get(0).getErpSync().equals(WmsConstant.ErpSyncEnum.INIT.getValue())) {
							// 使用已存在记录的billNo
							billNo = list.get(0).getBillNo();
						} else {
							// 生成新的billNo
							billNo = (String) FillRuleUtil.executeRule(FillRuleConstant.SEND_ORDER, new JSONObject());
						}
						// 记录该fbillno对应的billNo
						billNoMap.put(fbillno, billNo);
					}

					// 1. 构建主表 WmsSend
					WmsSend send = new WmsSend();
					send.setBillNo(billNo); // 使用一致的billNo
					// 主表字段映射
					send.setFbillno(obj.getString("fbillno"));  // 出库单号
					send.setFid(obj.getString("fid"));
					send.setWorkNo(obj.getString("fsrcbillno"));  // 来源单号
					send.setBillType("XSCK");
					send.setBillName("销售出库单");
					send.setBillStatus("1");

					// 单据日期
					String fdateStr = obj.getString("fdate");
					if (fdateStr != null) {
						send.setDeliveryDate(new SimpleDateFormat("yyyy-MM-dd").parse(fdateStr));
					}

					// 发货组织
					send.setOrganization(obj.getString("fstockorgid"));
					send.setOrganizationName(obj.getString("fstockorgidname"));
					send.setFromErp("1");
					send.setErpSync("0");

					// 发货区域 (已经不再保存，去除)
					send.setExpressInfo(obj.getString("flogcomname"));
					send.setDriver(obj.getString("fbijdysj"));
					send.setCargoLine(obj.getString("fbijhylx"));
					send.setPlateNumber(obj.getString("fbijcarnumber"));
					// 已从主表删除的字段不再设置
					send.setRemark(obj.getString("fentrynote"));
					send.setCustomerCode(obj.getString("fcustomerid"));
					send.setCustomerName(obj.getString("fcustomeridname"));
					send.setFid(obj.getString("fid"));
					// 这四个字段已从主表删除
					String fApproveDateStr = obj.getString("fapprovedate");
					if (fApproveDateStr != null) {
						// 使用 SimpleDateFormat 将日期字符串转换为 Date 对象
						send.setPostDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss").parse(fApproveDateStr));
					}else {
						send.setPostDate(new Date());
					}
					send.setWeightBridge(obj.getString("fbijdbstatus"));
					send.setDeliveryType(obj.getString("fdyfflag"));
					// 2. 构建子表 WmsSenddetail
					List<WmsSenddetail> detailList = new ArrayList<>();
					WmsSenddetail detail = new WmsSenddetail();

					// 行号(可自定义)
					detail.setLineNo(obj.getInteger("fseq"));

					// 单据号
					detail.setBillNo(billNo); // 使用一致的billNo
					// 品号
					detail.setItemCode(obj.getString("fmaterialidnumber"));
					// 品名
					detail.setItemName(obj.getString("fmaterialidname"));
					// 版本号
					detail.setVersion(obj.getString("fbomnumber"));
					detail.setLineState("1");

					// 单位
					detail.setItemUnit(obj.getString("funitid"));
					// 发货仓库
					detail.setWarehouse(obj.getString("fstockid"));
					// 计划出库数量
					detail.setPlanQty(obj.getDouble("fmustqty") == null ? 0.0 : obj.getDouble("fmustqty"));
					// 已出库数量
					detail.setActQty(0.0);
					detail.setDisQty(0.0);
					// 单件毛重
					detail.setSingleWeight(obj.getDouble("fbijgrossweight"));
					// 单件体积
					detail.setSingleVolume(obj.getDouble("fbijvolume"));
					// 物料总皮重
					detail.setTareWeight(obj.getDouble("fbijpackweight"));
					// 物料总毛重
					detail.setGrossWeight(obj.getDouble("fbijallgrossweight"));
					// 物料总体积
					detail.setVolume(obj.getDouble("fbijallvolume"));
					// 备注
					detail.setRemark(obj.getString("fentrynote"));
					detail.setFentryid(obj.getString("fentryid"));
					
					// 从主表获取并设置到明细表的字段
					detail.setSendMethod(obj.getString("hyfsdatavalue"));
					detail.setSalesman(obj.getString("fselleridname"));
					detail.setCargoOwner(obj.getString("fownername"));
					detail.setReceiveMan(obj.getString("fcustomeridname"));

					QueryWrapper<WmsSend> queryWrapper = new QueryWrapper<>();
					queryWrapper.eq("fbillno", send.getFbillno());
					//将创建时间最新的放在前面
					queryWrapper.orderByDesc("create_time");
					List<WmsSend> list = super.list(queryWrapper);
					if (list != null && !list.isEmpty() && list.get(0).getErpSync().equals(WmsConstant.ErpSyncEnum.INIT.getValue())) {
						// 获取当前明细单的主ID
						String mainId = list.get(0).getId();
						
						// 获取已经存在的明细数据
						List<WmsSenddetail> existingDetails = wmsSenddetailMapper.selectByMainId(mainId);
						
						// 检查是否存在重复记录（行号+物料编号）
						boolean isDuplicate = false;
						if(existingDetails != null && !existingDetails.isEmpty()) {
							isDuplicate = existingDetails.stream()
									.anyMatch(existingDetail -> 
											existingDetail.getLineNo() != null && 
											existingDetail.getLineNo().equals(detail.getLineNo()) && 
											existingDetail.getItemCode() != null && 
											existingDetail.getItemCode().equals(detail.getItemCode())
									);
						}
						
						// 如果不是重复记录，才进行插入
						if (!isDuplicate) {
							detail.setBillId(mainId);
							// 从当前记录和原始数据获取并设置字段
							detail.setSendMethod(obj.getString("hyfsdatavalue"));
							detail.setSalesman(obj.getString("fselleridname"));
							detail.setReceiveMan(obj.getString("freceivername"));
							detail.setCargoOwner(obj.getString("fownername"));
							wmsSenddetailMapper.insert(detail);
						} else {
							log.info("跳过重复的出库明细记录：行号 [{}]，物料编号 [{}]", detail.getLineNo(), detail.getItemCode());
						}
					} else {
						detailList.add(detail);
						this.saveMain(send, detailList);
					}
				}
			}
			log.info("销售出库单新增成功");
		} catch (Exception e) {
			log.error("销售出库单新增异常:", e);
			throw new RuntimeException("销售出库单新增异常" + e.getMessage());
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public JSONObject submitRecord(String id, boolean b) {
		if (StringUtils.isBlank(id)){
			throw new RuntimeException("出库单id不能为空");
		}
		//根据id获取出库单信息
		WmsSend wmsSend = wmsSendMapper.selectById(id);
		if (wmsSend == null){
			throw new RuntimeException("未查询到出库单信息");
		}
		if (wmsSend.getErpSync() != null && wmsSend.getErpSync().equals(WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue())){
			throw new RuntimeException("该出库单已同步ERP，无法再次提交");
		}
		//根据出库单id获取出库单明细
		List<WmsSenddetail> wmsSenddetails = wmsSenddetailMapper.selectByMainId(id);
		if (wmsSenddetails == null ||  wmsSenddetails.isEmpty()){
			throw new RuntimeException("未查询到出库单明细信息");
		}
		//将单据明细状态为部分发运和全部发运的出库单明细筛出来
		List<WmsSenddetail> wmsSenddetailList = wmsSenddetails.stream().filter(wmsSenddetail -> "3".equals(wmsSenddetail.getLineState()) || "4".equals(wmsSenddetail.getLineState())).collect(Collectors.toList());
		if ( wmsSenddetailList.isEmpty()){
			throw new RuntimeException("未查询到部分发运或全部发运的出库单明细信息");
		}
		//将actQty不为0的出库单明细筛出来
		wmsSenddetailList = wmsSenddetailList.stream().filter(wmsSenddetail -> wmsSenddetail.getActQty() != 0).collect(Collectors.toList());
		if ( wmsSenddetailList.isEmpty()){
			throw new RuntimeException("未查询到实发数量不为0的出库单明细信息");
		}
		//进行提交操作
		MesInterface mi = mesInterfaceService.getOne(
				new QueryWrapper<MesInterface>().eq("interface_code", "JK027")
		);
		if (mi == null) {
			throw new RuntimeException("未找到MES接口配置信息: JK027");
		}
		if(!"1".equals(mi.getInterfaceStatus())) {
			throw new RuntimeException("接口JK027未启用！");
		}
		if(!"POST".equalsIgnoreCase(mi.getRequestMethod())) {
			throw new RuntimeException("接口JK027的请求方式不是POST！");
		}
		String accessToken = redisTemplate.opsForValue().get("mes_login_access_token");
		if (accessToken == null) {
			throw new RuntimeException("未获取到MES登录token，请先调用登录接口！");
		}

		// 构建请求参数
		JSONObject requestParam = buildRequestParam(wmsSend, wmsSenddetailList,b);

		// 调用接口
		JSONObject jsonObject = doPost(mi.getInterfaceUrl(), accessToken, requestParam);

		// 处理响应
		return handleResponse(jsonObject, wmsSend, id);
	}

	private JSONObject doPost(String url, String token, JSONObject requestBody) {
		return HttpClientUtil.doPostEnhanced(url, token, requestBody, baseCommonService, "MES发货", "syncSendData");
	}

	private JSONObject buildRequestParam(WmsSend wmsSend, List<WmsSenddetail> wmsSenddetailList, boolean b) {
		//获取当前登录用户
		LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		JSONObject requestParam = new JSONObject();
		requestParam.put("ignoreInterationFlag", b);
		requestParam.put("billNo",wmsSend.getFbillno());
		requestParam.put("creator", loginUser.getUsername());
		//遍历wmsSenddetailList
		JSONArray jsonArray = new JSONArray();
		for (WmsSenddetail wmsSenddetail : wmsSenddetailList) {
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("frealqty",wmsSenddetail.getActQty());
			jsonObject.put("fsid",wmsSenddetail.getFentryid());
			jsonArray.add(jsonObject);
		}
		requestParam.put("entityList",jsonArray);
		log.info("销售出库单同步请求参数: {}", requestParam.toJSONString());
		return requestParam;
	}

	private String getLocalIpAddress() {
		try {
			InetAddress localHost = InetAddress.getLocalHost();
			return localHost.getHostAddress();
		} catch (UnknownHostException e) {
			return "未知";
		}
	}

	private JSONObject handleResponse(JSONObject responseJson, WmsSend wmsSend, String id) {
		log.info("销售出库单同步结果: {}", responseJson.toJSONString());
		boolean isSuccess = responseJson.getBoolean("isSuccess");
		JSONObject result = new JSONObject();
		
		// 提取需要保存的消息内容
		String syncMessage = "";
		if (isSuccess) {
			JSONArray successEntities = responseJson.getJSONArray("successEntitys");
			if (successEntities != null && !successEntities.isEmpty()) {
				syncMessage = "同步成功，单据已提交到上游系统";
				wmsSend.setErpSync(WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue());
				wmsSend.setUpdateTime(new Date());
				
				// 从successEntities中获取第一个元素的id和number并赋值
				if (successEntities.size() > 0) {
					JSONObject firstEntity = successEntities.getJSONObject(0);
					if (firstEntity != null) {
						// 将id和number分别赋值给k3Id和k3BillNo
						if (firstEntity.containsKey("id")) {
							wmsSend.setK3Id(firstEntity.getString("id"));
						}
						if (firstEntity.containsKey("number")) {
							wmsSend.setK3BillNo(firstEntity.getString("number"));
						}
					}
				}
				
				log.info("销售出库单同步成功，ID: {}", id);
				result.put("success", true);
				result.put("message", "销售出库单同步成功");
			}
		} else {
			JSONArray errors = responseJson.getJSONArray("errors");
			int msgCode = responseJson.getIntValue("msgCode");
			String fieldName = responseJson.getString("fieldName");
			String errorMessage = (errors != null) ? errors.toJSONString() : "未知错误";
			
			if (msgCode == 11 && "AbstractInteractionResult".equals(fieldName)) {
				syncMessage = "更新库存时出现可以忽略的异常数据";
				result.put("success", false);
				result.put("message", "更新库存时出现可以忽略的异常数据，是否继续？");
				//强制提交标记
				result.put("forceSubmit", true);
			} else {
				syncMessage = "同步失败: " + errorMessage;
				log.error("其他出库单同步失败，ID: {}，错误信息: {}", id, errorMessage);
				result.put("success", false);
				result.put("message", "其他出库单同步失败: " + errorMessage);
				//设置erpSync为同步失败
				wmsSend.setErpSync("3");
				wmsSend.setUpdateTime(new Date());
			}
		}
		
		// 确保消息长度不超过数据库限制(VARCHAR(255))
		if (syncMessage.length() > 255) {
			syncMessage = syncMessage.substring(0, 252) + "...";
		}
		
		// 设置同步消息
		wmsSend.setSyncMessage(syncMessage);
		wmsSendMapper.updateById(wmsSend);
		
		return result;
	}
}

