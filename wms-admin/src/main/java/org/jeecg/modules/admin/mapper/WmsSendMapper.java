package org.jeecg.modules.admin.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.admin.entity.WmsSend;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.admin.vo.WmsInAndOutStatisticsPage;

/**
 * @Description: 出库主单据
 * @Author: jeecg-boot
 * @Date:   2024-06-19
 * @Version: V1.0
 */
public interface WmsSendMapper extends BaseMapper<WmsSend> {

    List<WmsInAndOutStatisticsPage> getSendReceive(String startTime, String endTime, String itemNumber, String itemName);
}
