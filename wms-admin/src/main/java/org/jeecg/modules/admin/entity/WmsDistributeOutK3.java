package org.jeecg.modules.admin.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 分布式调出单
 * @Author: jeecg-boot
 * @Date:   2024-10-26
 * @Version: V1.0
 */
@Data
@TableName("wms_distribute_out_k3")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="wms_distribute_out_k3对象", description="分布式调出单")
public class WmsDistributeOutK3 implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**单据标识*/
	@Excel(name = "单据标识", width = 15)
    @ApiModelProperty(value = "单据标识")
    private java.lang.Integer serialNumber;
	/**行号*/
	@Excel(name = "行号", width = 15)
    @ApiModelProperty(value = "行号")
    private java.lang.Integer lineNo;
	/**调拨单号*/
	@Excel(name = "调拨单号", width = 15)
    @ApiModelProperty(value = "调拨单号")
    private java.lang.String workNo;
	/**单据名称*/
	@Excel(name = "单据名称", width = 15)
    @ApiModelProperty(value = "单据名称")
    private java.lang.String billName;
	/**单据类型*/
	@Excel(name = "单据类型", width = 15, dicCode = "distribute_out_bill_type")
	@Dict(dicCode = "distribute_out_bill_type")
    @ApiModelProperty(value = "单据类型")
    private java.lang.String billType;
	/**单据状态*/
	@Excel(name = "单据状态", width = 15, dicCode = "distribute_out_bill_status")
	@Dict(dicCode = "distribute_out_bill_status")
    @ApiModelProperty(value = "单据状态")
    private java.lang.String billStatus;
	/**物料编号*/
	@Excel(name = "物料编号", width = 15)
    @ApiModelProperty(value = "物料编号")
    private java.lang.String itemCode;
	/**物料名称*/
	@Excel(name = "物料名称", width = 15)
    @ApiModelProperty(value = "物料名称")
    private java.lang.String itemName;
	/**调出仓库名称*/
	@Excel(name = "调出仓库名称", width = 15)
    @ApiModelProperty(value = "调出仓库名称")
    private java.lang.String warehouseOut;
	/**调入仓库名称*/
	@Excel(name = "调入仓库名称", width = 15)
    @ApiModelProperty(value = "调入仓库名称")
    private java.lang.String warehouseIn;
	/**调出货主名称*/
	@Excel(name = "调出货主名称", width = 15)
    @ApiModelProperty(value = "调出货主名称")
    private java.lang.String cargoOwnerOut;
	/**调入货主名称*/
	@Excel(name = "调入货主名称", width = 15)
    @ApiModelProperty(value = "调入货主名称")
    private java.lang.String cargoOwnerIn;
	/**调拨单位*/
	@Excel(name = "调拨单位", width = 15)
    @ApiModelProperty(value = "调拨单位")
    private java.lang.String distributeUnit;
	/**调拨数量*/
	@Excel(name = "调拨数量", width = 15)
    @ApiModelProperty(value = "调拨数量")
    private java.lang.Double distributeQty;
	/**调拨日期*/
	@Excel(name = "调拨日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "调拨日期")
    private java.util.Date distributeDate;
	/**版本号*/
	@Excel(name = "版本号", width = 15)
    @ApiModelProperty(value = "版本号")
    private java.lang.String version;
	/**审核人*/
	@Excel(name = "审核人", width = 15)
    @ApiModelProperty(value = "审核人")
    private java.lang.String checkBy;
	/**审核时间*/
	@Excel(name = "审核时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private java.util.Date checkTime;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
}
