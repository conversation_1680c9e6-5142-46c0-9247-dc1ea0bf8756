package org.jeecg.modules.admin.service.impl;

import org.jeecg.modules.admin.entity.WmsProrequistDetail;
import org.jeecg.modules.admin.mapper.WmsProrequistDetailMapper;
import org.jeecg.modules.admin.service.IWmsProrequistDetailService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 生产单据明细
 * @Author: jeecg-boot
 * @Date:   2024-11-06
 * @Version: V1.0
 */
@Service
public class WmsProrequistDetailServiceImpl extends ServiceImpl<WmsProrequistDetailMapper, WmsProrequistDetail> implements IWmsProrequistDetailService {
	
	@Autowired
	private WmsProrequistDetailMapper wmsProrequistDetailMapper;
	
	@Override
	public List<WmsProrequistDetail> selectByMainId(String mainId) {
		return wmsProrequistDetailMapper.selectByMainId(mainId);
	}
}
