package org.jeecg.modules.admin.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.log4j.Log4j2;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.dto.LogDTO;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.admin.entity.MesInterface;
import org.jeecg.modules.admin.entity.WmsOtherIn;
import org.jeecg.modules.admin.mapper.WmsOtherInMapper;
import org.jeecg.modules.admin.service.IMesInterfaceService;
import org.jeecg.modules.admin.service.IWmsOtherInService;
import org.jeecg.modules.base.service.BaseCommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;

// 引入需要的HttpClientUtil类
import org.jeecg.modules.admin.util.HttpClientUtil;

/**
 * @Description: 其他入库单
 * @Author: jeecg-boot
 * @Date:   2024-11-04
 * @Version: V1.0
 */
@Service
@Log4j2
public class WmsOtherInServiceImpl extends ServiceImpl<WmsOtherInMapper, WmsOtherIn> implements IWmsOtherInService {
    @Autowired
    private WmsOtherInMapper wmsOtherInMapper;
    @Autowired
    private IMesInterfaceService mesInterfaceService;  // 假设有这个Service来查接口配置
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private BaseCommonService baseCommonService;
    @Override
    public JSONObject submitRecord(String id) {
        log.info("开始提交【其他入库单】至K3，id: {}", id);
        // 1. 根据id查询主表
        WmsOtherIn wmsOtherIn = wmsOtherInMapper.selectById(id);
        if (wmsOtherIn == null) {
            throw new RuntimeException("其他入库单不存在，id:" + id);
        }

        // 2. 根据单据状态判断是否允许提交（示例：若需要"已完成"状态才能提交）
//        if (!"4".equals(wmsOtherIn.getBillStatus())) {
//            throw new RuntimeException("其他入库单非完成状态，无法提交！");
//        }

        // 3. 判断是否已同步
        //   假设 erpSync=="2" 表示已同步
        if ("2".equals(wmsOtherIn.getErpSync())) {
            throw new RuntimeException("其他入库单已同步，id:" + id);
        }

        // 4. 获取接口配置：JK023 (其他入库单回传)
        MesInterface mi = mesInterfaceService.getOne(
                new QueryWrapper<MesInterface>().eq("interface_code", "JK023")
        );
        if (mi == null) {
            throw new RuntimeException("接口配置不存在: JK023");
        }

        // 4.1 校验启用状态
        if (!"1".equals(mi.getInterfaceStatus())) {
            throw new RuntimeException("接口JK023未启用！");
        }

        // 4.2 请求方式校验
        if (!"POST".equalsIgnoreCase(mi.getRequestMethod())) {
            throw new RuntimeException("接口JK023的请求方式不是POST！");
        }

        // 5. 获取 MES/K3 Token
        String accessToken = redisTemplate.opsForValue().get("mes_login_access_token");
        if (accessToken == null) {
            // 如果没有获取到MES登录token，则调用performLoginAndStoreToken方法获取
            try {
                log.info("未获取到MES登录token，正在调用登录接口获取...");
                mesInterfaceService.performLoginAndStoreToken();
                // 重新获取token
                accessToken = redisTemplate.opsForValue().get("mes_login_access_token");
                if (accessToken == null) {
                    throw new RuntimeException("登录接口调用成功但仍未获取到MES登录token！");
                }
                log.info("已成功获取MES登录token");
            } catch (IOException e) {
                throw new RuntimeException("调用MES登录接口时发生异常: " + e.getMessage(), e);
            }
        }

        // 6. 构建请求参数
        JSONObject requestParam = buildRequestParam(wmsOtherIn);

        // 7. 发起接口请求
        String url = mi.getInterfaceUrl();  // e.g. "http://mes2.gxty.com/stage-api/schedule/transferOrder/other"
        JSONObject responseJson = doPost(url, accessToken, requestParam);

        // 8. 处理返回
        return handleResponse(responseJson, wmsOtherIn);
    }

    private JSONObject buildRequestParam(WmsOtherIn wmsOtherIn) {
        //获取当前登录用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 构建主表 JSON
        JSONObject requestParam = new JSONObject();

        // 1) date - 入库日期
        //    示例中使用 wmsOtherIn.getReceiveDate()
        String dateStr = "";
        if (wmsOtherIn.getReceiveDate() != null) {
            dateStr = new SimpleDateFormat("yyyy-MM-dd").format(wmsOtherIn.getReceiveDate());
        }
        requestParam.put("date", dateStr);

        // 2) 创建人、审核人：接口文档说是 int 类型 ID，这里示例中直接传字符串
        //    若项目能拿到实际的用户ID，可做转换；否则可先传一个占位。
        requestParam.put("creator", loginUser.getUsername());
        requestParam.put("auditor", "1"); // 或者空字符串，根据业务决定

        // 3) 是否忽略审核
        requestParam.put("ignoreInterationFlag", true);

        // 4) fstockdirect - 库存方向
        //    文档示例写的是 "RETURN"，
        //    如果实际业务需要"IN"或其他值，需要和接口对方确认
        requestParam.put("fstockdirect", "RETURN");

        // 5) 供应商编码 or 部门编码（两者至少有一个）
        //    示例：先从 wmsOtherIn.getRemark() 或其他地方获取，也可能是表里另有字段
        //    如果没有供应商，就传部门。这里仅作示例：
        requestParam.put("fsuppliernumber", "001513221"); // 示例值
        requestParam.put("fdeptnumber", "");              // 部门编码留空

        // 6) fstockergroupnumber - 库存组编码、以及仓管员编码等
        //    看业务实际是否需要，如果没有就传空字符串
        requestParam.put("fstockergroupnumber", "");
        // 备注（非必填）
        requestParam.put("fnote", wmsOtherIn.getRemark() == null ? "" : wmsOtherIn.getRemark());

        // 7) 构建明细信息 list
        JSONArray listArray = new JSONArray();
        // 如果项目中有明细表 (like WmsOtherInDetail)，
        // 应该一次查询多个明细，这里仅以主表字段做示例（只有一个品号、仓库...）
        JSONObject detailItem = new JSONObject();

        // fmaterialnumber - 物料编码
        detailItem.put("fmaterialnumber", wmsOtherIn.getItemCode() == null ? "" : wmsOtherIn.getItemCode());
        // fbijauxqty - 实收数量
        detailItem.put("fbijauxqty", wmsOtherIn.getReceiveQty() == null ? 0 : wmsOtherIn.getReceiveQty());
        // fbijperrate - 含量（假设默认1，如果项目中有单件重量或其他字段可替代）
        detailItem.put("fbijperrate", wmsOtherIn.getSingleWeight() == null ? 1 : wmsOtherIn.getSingleWeight());

        // fstocknumber - 收货仓库编码
        detailItem.put("fstocknumber",
                wmsOtherIn.getWarehouseCodeIn() == null ? "" : wmsOtherIn.getWarehouseCodeIn());

        listArray.add(detailItem);

        requestParam.put("list", listArray);

        return requestParam;
    }

    /**
     * 处理接口返回的 JSON，并根据结果更新本地数据库
     */
    private JSONObject handleResponse(JSONObject responseJson, WmsOtherIn wmsOtherIn) {
        JSONObject result = new JSONObject();

        boolean isSuccess = responseJson.getBooleanValue("isSuccess");
        int msgCode = responseJson.getIntValue("msgCode");
        JSONArray errors = responseJson.getJSONArray("errors");

        if (isSuccess) {
            // 如果成功，获取单号
            JSONArray successEntitys = responseJson.getJSONArray("successEntitys");
            String k3Number = "";
            if (successEntitys != null && !successEntitys.isEmpty()) {
                JSONObject entity = successEntitys.getJSONObject(0);
                k3Number = entity.getString("number");
            }
            // 更新数据库记录
            wmsOtherIn.setWorkNo(k3Number);
            wmsOtherIn.setErpSync("2"); // 2表示已同步
            wmsOtherInMapper.updateById(wmsOtherIn);

            result.put("success", true);
            result.put("message", "其他入库单同步成功");
            result.put("k3Number", k3Number);
            log.info("其他入库单同步成功, ID: {}, K3单号: {}", wmsOtherIn.getId(), k3Number);
        } else {
            // msgCode = 11 表示可忽略异常（根据接口文档定义）
            if (msgCode == 11) {
                result.put("success", false);
                result.put("message", "出现可忽略的异常数据，是否强制提交？");
                // 标识前端是否可再次调用接口
                result.put("forceSubmit", true);
            } else {
                // 其他错误
                String errorMessage = (errors != null && !errors.isEmpty())
                        ? errors.toJSONString()
                        : "未知错误";
                result.put("success", false);
                result.put("message", "其他入库单同步失败: " + errorMessage);
                log.error("其他入库单同步失败, ID: {}, 错误信息: {}", wmsOtherIn.getId(), errorMessage);
            }
            // 更新同步状态为失败 "3"
            wmsOtherIn.setErpSync("3");
            wmsOtherInMapper.updateById(wmsOtherIn);
        }

        return result;
    }

    /**
     * 通用的POST请求示例
     * @param url   接口URL
     * @param token Bearer Token
     * @param requestBody  请求体(JSON)
     */
    private JSONObject doPost(String url, String token, JSONObject requestBody) {
        return HttpClientUtil.doPostEnhanced(url, token, requestBody, baseCommonService, "MES其他入库", "syncOtherInData");
    }

    /**
     * 获取本机 IP 地址
     */
    private String getLocalIpAddress() {
        try {
            InetAddress localHost = InetAddress.getLocalHost();
            return localHost.getHostAddress();
        } catch (UnknownHostException e) {
            return "未知";
        }
    }
}
