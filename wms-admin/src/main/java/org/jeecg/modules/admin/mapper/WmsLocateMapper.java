package org.jeecg.modules.admin.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.admin.entity.WmsLocate;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.admin.vo.WmsLocateByWcsPage;
import org.jeecg.modules.admin.vo.WmsLocatePage;

/**
 * @Description: 货位表
 * @Author: jeecg-boot
 * @Date:   2024-06-15
 * @Version: V1.0
 */
public interface WmsLocateMapper extends BaseMapper<WmsLocate> {
    //获取所有货位的排号信息（去重）
    List<String> selectAllRow(@Param("zoneCode") String zoneCode);
    List<String> selectAllColumn(@Param("zoneCode") String zoneCode);
    List<String> selectAllLevel(@Param("zoneCode") String zoneCode);

    List<WmsLocatePage> queryAll(String rowNo,String colNo,String levelNo);

    List<WmsLocateByWcsPage> queryPageListByWcs(@Param(Constants.WRAPPER) QueryWrapper<WmsLocate> queryWrapper);
    
}
