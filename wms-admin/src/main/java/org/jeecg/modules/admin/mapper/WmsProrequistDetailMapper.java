package org.jeecg.modules.admin.mapper;

import java.util.List;
import org.jeecg.modules.admin.entity.WmsProrequistDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 生产单据明细
 * @Author: jeecg-boot
 * @Date:   2024-11-06
 * @Version: V1.0
 */
public interface WmsProrequistDetailMapper extends BaseMapper<WmsProrequistDetail> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<WmsProrequistDetail>
   */
	public List<WmsProrequistDetail> selectByMainId(@Param("mainId") String mainId);
}
