package org.jeecg.modules.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.admin.entity.WmsSpecMatchItem;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 物料信息
 * @Author: jeecg-boot
 * @Date:   2024-07-15
 * @Version: V1.0
 */
public interface IWmsSpecMatchItemService extends IService<WmsSpecMatchItem> {

    void syncData();

    IPage<WmsSpecMatchItem> queryPageListWithJoin(Page<WmsSpecMatchItem> page, QueryWrapper<WmsSpecMatchItem> queryWrapper);

    void editMain(WmsSpecMatchItem wmsSpecMatchItem);
}
