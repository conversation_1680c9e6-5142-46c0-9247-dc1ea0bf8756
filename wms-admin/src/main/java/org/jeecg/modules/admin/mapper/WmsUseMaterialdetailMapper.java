package org.jeecg.modules.admin.mapper;

import java.util.List;
import org.jeecg.modules.admin.entity.WmsUseMaterialdetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 用料明细
 * @Author: jeecg-boot
 * @Date:   2024-10-28
 * @Version: V1.0
 */
public interface WmsUseMaterialdetailMapper extends BaseMapper<WmsUseMaterialdetail> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<WmsUseMaterialdetail>
   */
	public List<WmsUseMaterialdetail> selectByMainId(@Param("mainId") String mainId);
}
