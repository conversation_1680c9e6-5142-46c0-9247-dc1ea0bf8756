package org.jeecg.modules.admin.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.admin.entity.WmsTaskQueueAgv;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: AGV任务
 * @Author: jeecg-boot
 * @Date:   2024-08-20
 * @Version: V1.0
 */
public interface WmsTaskQueueAgvMapper extends BaseMapper<WmsTaskQueueAgv> {
    IPage<WmsTaskQueueAgv> page(Page<WmsTaskQueueAgv> page, @Param(Constants.WRAPPER) QueryWrapper<WmsTaskQueueAgv> queryWrapper);
}
