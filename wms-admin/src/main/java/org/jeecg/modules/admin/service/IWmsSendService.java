package org.jeecg.modules.admin.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.jeecg.modules.admin.entity.WmsSenddetail;
import org.jeecg.modules.admin.entity.WmsSend;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.admin.vo.WmsInAndOutStatisticsPage;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @Description: 出库主单据
 * @Author: jeecg-boot
 * @Date:   2024-06-19
 * @Version: V1.0
 */
public interface IWmsSendService extends IService<WmsSend> {

	/**
	 * 添加一对多
	 *
	 * @param wmsSend
	 * @param wmsSenddetailList
	 */
	public void saveMain(WmsSend wmsSend,List<WmsSenddetail> wmsSenddetailList) ;
	
	/**
	 * 修改一对多
	 *
	 * @param wmsSend
	 * @param wmsSenddetailList
	 */
	public void updateMain(WmsSend wmsSend,List<WmsSenddetail> wmsSenddetailList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

	Map<String, Object> getWeekSend(String startTime, String endTime);

	Map<String, Object> getDaySend(String date);

    List<WmsInAndOutStatisticsPage> getSendReceive(String startTime, String endTime, String itemNumber, String itemName);

    void receiveSalesOut(JSONArray dataArr);

	JSONObject submitRecord(String id, boolean b);
}
