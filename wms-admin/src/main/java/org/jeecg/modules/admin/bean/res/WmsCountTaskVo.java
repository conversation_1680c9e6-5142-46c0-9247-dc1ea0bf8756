package org.jeecg.modules.admin.bean.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class WmsCountTaskVo {

    @ApiModelProperty(value = "物料编码")
    private String itemCode;

    @ApiModelProperty(value = "物料名称")
    private String itemName;

    @ApiModelProperty(value = "批次")
    private String batchCode;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "账面总数量")
    private String sumSystemQuantity;

    @ApiModelProperty(value = "盘点总数量")
    private String sumCountedQuantity;

    @ApiModelProperty(value = "差异总数量")
    private Double sumDifferenceQuantity;

    @ApiModelProperty(value = "确认总数量")
    private String sumConfirmQuantity;

}
