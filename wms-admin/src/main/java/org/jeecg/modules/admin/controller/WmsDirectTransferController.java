package org.jeecg.modules.admin.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.entity.WmsDirectTransfer;
import org.jeecg.modules.admin.service.IWmsDirectTransferService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 直接调拨单
 * @Author: jeecg-boot
 * @Date:   2024-11-04
 * @Version: V1.0
 */
@Api(tags="直接调拨单")
@RestController
@RequestMapping("/admin/wmsDirectTransfer")
@Slf4j
public class WmsDirectTransferController extends JeecgController<WmsDirectTransfer, IWmsDirectTransferService> {
	@Autowired
	private IWmsDirectTransferService wmsDirectTransferService;
	
	/**
	 * 分页列表查询
	 *
	 * @param wmsDirectTransfer
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "直接调拨单-分页列表查询")
	@ApiOperation(value="直接调拨单-分页列表查询", notes="直接调拨单-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WmsDirectTransfer>> queryPageList(WmsDirectTransfer wmsDirectTransfer,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WmsDirectTransfer> queryWrapper = QueryGenerator.initQueryWrapper(wmsDirectTransfer, req.getParameterMap());
		Page<WmsDirectTransfer> page = new Page<WmsDirectTransfer>(pageNo, pageSize);
		IPage<WmsDirectTransfer> pageList = wmsDirectTransferService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param wmsDirectTransfer
	 * @return
	 */
	@AutoLog(value = "直接调拨单-添加")
	@ApiOperation(value="直接调拨单-添加", notes="直接调拨单-添加")
	@RequiresPermissions("admin:wms_direct_transfer:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WmsDirectTransfer wmsDirectTransfer) {
		wmsDirectTransferService.save(wmsDirectTransfer);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param wmsDirectTransfer
	 * @return
	 */
	@AutoLog(value = "直接调拨单-编辑")
	@ApiOperation(value="直接调拨单-编辑", notes="直接调拨单-编辑")
	@RequiresPermissions("admin:wms_direct_transfer:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WmsDirectTransfer wmsDirectTransfer) {
		wmsDirectTransferService.updateById(wmsDirectTransfer);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "直接调拨单-通过id删除")
	@ApiOperation(value="直接调拨单-通过id删除", notes="直接调拨单-通过id删除")
	@RequiresPermissions("admin:wms_direct_transfer:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		wmsDirectTransferService.removeById(id);
		return Result.OK("删除成功!");
	}


	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "直接调拨单-批量删除")
	@ApiOperation(value="直接调拨单-批量删除", notes="直接调拨单-批量删除")
	@RequiresPermissions("admin:wms_direct_transfer:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.wmsDirectTransferService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "直接调拨单-通过id查询")
	@ApiOperation(value="直接调拨单-通过id查询", notes="直接调拨单-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WmsDirectTransfer> queryById(@RequestParam(name="id",required=true) String id) {
		WmsDirectTransfer wmsDirectTransfer = wmsDirectTransferService.getById(id);
		if(wmsDirectTransfer==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(wmsDirectTransfer);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param wmsDirectTransfer
    */
    @RequiresPermissions("admin:wms_direct_transfer:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WmsDirectTransfer wmsDirectTransfer) {
        return super.exportXls(request, wmsDirectTransfer, WmsDirectTransfer.class, "直接调拨单");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:wms_direct_transfer:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WmsDirectTransfer.class);
    }

	 /**
	  * 提交直接调拨单
	  */
	 @AutoLog(value = "直接调拨单-提交")
	 @ApiOperation(value="直接调拨单-提交", notes="直接调拨单-提交")
	 @GetMapping(value = "/submitRecord")
	 public Result<JSONObject> submitRecord(@RequestParam(name="id",required=true) String id,@RequestParam(name="flag",required=false) boolean flag) {
		 JSONObject jsonObject = wmsDirectTransferService.submitRecord(id, flag);
		 return Result.OK(jsonObject);
	 }

	 /**
	  * 提交直接调拨单
	  */
	 @AutoLog(value = "直接调拨单-出库")
	 @ApiOperation(value="直接调拨单-出库", notes="直接调拨单-出库")
	 @GetMapping(value = "/outRecord")
	 public Result<String> submitRecord(@RequestParam(name="id",required=true) String id) {
		 JSONObject jsonObject = wmsDirectTransferService.outRecord(id);
		 if (jsonObject == null) {
			 return Result.error("出库失败！");
		 }else if (jsonObject.getBoolean("result")) {
			 return Result.ok("出库成功！");
		 }else {
			 return Result.error("出库失败！" + jsonObject.getString("message"));
		 }
	 }

 }
