package org.jeecg.modules.admin.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.entity.WmsContainer;
import org.jeecg.modules.admin.service.IWmsContainerService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 托盘表
 * @Author: jeecg-boot
 * @Date:   2024-11-27
 * @Version: V1.0
 */
@Api(tags="托盘表")
@RestController
@RequestMapping("/admin/wmsContainer")
@Slf4j
public class WmsContainerController extends JeecgController<WmsContainer, IWmsContainerService> {
	@Autowired
	private IWmsContainerService wmsContainerService;
	
	/**
	 * 分页列表查询
	 *
	 * @param wmsContainer
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "托盘表-分页列表查询")
	@ApiOperation(value="托盘表-分页列表查询", notes="托盘表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WmsContainer>> queryPageList(WmsContainer wmsContainer,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WmsContainer> queryWrapper = QueryGenerator.initQueryWrapper(wmsContainer, req.getParameterMap());
		Page<WmsContainer> page = new Page<WmsContainer>(pageNo, pageSize);
		IPage<WmsContainer> pageList = wmsContainerService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param wmsContainer
	 * @return
	 */
	@AutoLog(value = "托盘表-添加")
	@ApiOperation(value="托盘表-添加", notes="托盘表-添加")
	@RequiresPermissions("admin:wms_container:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WmsContainer wmsContainer) {
		wmsContainerService.save(wmsContainer);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param wmsContainer
	 * @return
	 */
	@AutoLog(value = "托盘表-编辑")
	@ApiOperation(value="托盘表-编辑", notes="托盘表-编辑")
	@RequiresPermissions("admin:wms_container:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WmsContainer wmsContainer) {
		wmsContainerService.updateById(wmsContainer);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "托盘表-通过id删除")
	@ApiOperation(value="托盘表-通过id删除", notes="托盘表-通过id删除")
	@RequiresPermissions("admin:wms_container:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		wmsContainerService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "托盘表-批量删除")
	@ApiOperation(value="托盘表-批量删除", notes="托盘表-批量删除")
	@RequiresPermissions("admin:wms_container:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.wmsContainerService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "托盘表-通过id查询")
	@ApiOperation(value="托盘表-通过id查询", notes="托盘表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WmsContainer> queryById(@RequestParam(name="id",required=true) String id) {
		WmsContainer wmsContainer = wmsContainerService.getById(id);
		if(wmsContainer==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(wmsContainer);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param wmsContainer
    */
    @RequiresPermissions("admin:wms_container:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WmsContainer wmsContainer) {
        return super.exportXls(request, wmsContainer, WmsContainer.class, "托盘表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:wms_container:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WmsContainer.class);
    }

}
