package org.jeecg.modules.admin.service;

import org.jeecg.modules.admin.entity.WmsOtherOut1Detail;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 其他出库单明细
 * @Author: jeecg-boot
 * @Date:   2024-12-21
 * @Version: V1.0
 */
public interface IWmsOtherOut1DetailService extends IService<WmsOtherOut1Detail> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<WmsOtherOut1Detail>
	 */
	public List<WmsOtherOut1Detail> selectByMainId(String mainId);
}
