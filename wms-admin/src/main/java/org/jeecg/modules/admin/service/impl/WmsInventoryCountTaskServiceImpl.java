package org.jeecg.modules.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.admin.bean.res.WmsInventoryCountTaskVo;
import org.jeecg.modules.admin.constant.WmsConstant;
import org.jeecg.modules.admin.entity.*;
import org.jeecg.modules.admin.enums.CountTaskStatusEnum;
import org.jeecg.modules.admin.enums.InvStateEnum;
import org.jeecg.modules.admin.enums.InventoryCountPlanStatusEnum;
import org.jeecg.modules.admin.mapper.WmsInventoryCountPlanMapper;
import org.jeecg.modules.admin.mapper.WmsInventoryCountTaskMapper;
import org.jeecg.modules.admin.mapper.WmsLocateMapper;
import org.jeecg.modules.admin.mapper.WmsStockdetailMapper;
import org.jeecg.modules.admin.service.IWmsInventoryCountTaskService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 盘点任务
 * @Author: jeecg-boot
 * @Date:   2024-12-20
 * @Version: V1.0
 */
@Service
public class WmsInventoryCountTaskServiceImpl extends ServiceImpl<WmsInventoryCountTaskMapper, WmsInventoryCountTask> implements IWmsInventoryCountTaskService {

    @Resource
    private WmsStockdetailMapper wmsStockdetailMapper;

    @Resource
    private WmsInventoryCountPlanMapper wmsInventoryCountPlanMapper;

    @Resource
    private WmsLocateMapper wmsLocateMapper;

    @Override
    public WmsInventoryCountTaskVo getCountTask(String param) {
        if (StringUtils.isEmpty(param)) {
            throw new JeecgBootException("请输入盘点任务号或托盘号！");
        }
        QueryWrapper<WmsInventoryCountTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(WmsInventoryCountTask::getTaskCode, param)
                .or().eq(WmsInventoryCountTask::getLpn, param);
        List<WmsInventoryCountTask> countTaskList = this.list(queryWrapper);

        if(CollectionUtils.isEmpty(countTaskList)) {
            throw new JeecgBootException("盘点任务单不存在或其对应的托盘不存在");
        }

        boolean noCreate = countTaskList.stream().anyMatch(item -> !CountTaskStatusEnum.PANDIANCHUANGJIAN.getCode().equals(item.getTaskStatus()));
        if (noCreate) {
            throw new JeecgBootException("PDA盘点任务须为：盘点创建状态");
        }

        List<String> batchCodes = countTaskList.stream().map(WmsInventoryCountTask::getBatchCode).filter(
                batchNo -> !StringUtils.isEmpty(batchNo)).distinct().collect(Collectors.toList());

        WmsInventoryCountTaskVo taskVo = new WmsInventoryCountTaskVo();
        BeanUtils.copyProperties(countTaskList.get(WmsConstant.INT_ZERO), taskVo);
        taskVo.setBatchCodes(batchCodes);
        return taskVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finishStockCheckTask(WmsInventoryCountTaskVo wmsInventoryCountTaskVo) {
        // 验证参数信息
        validStockCheckReq(wmsInventoryCountTaskVo);

        // 验证库存信息是否存在
        WmsStockdetail stockDetail = validStockCheckStock(wmsInventoryCountTaskVo);

        // 查询库位是否是立库库位
        WmsLocate wmsLocate = wmsLocateMapper.selectOne(new QueryWrapper<WmsLocate>()
                .lambda().eq(WmsLocate::getLocateCode, wmsInventoryCountTaskVo.getLocateCode()));

        // 验证盘点任务
        WmsInventoryCountTask wmsInventoryCountTask = validStockCheckTask(wmsInventoryCountTaskVo);

        // 更新库存状态
        updateStockCheckStock(stockDetail, wmsLocate, wmsInventoryCountTaskVo);

        // 更新盘点任务信息
        updateStockCheckTask(wmsInventoryCountTaskVo, wmsInventoryCountTask, stockDetail);

        // 对盘点单状态更新
        updateStockCheckStatus(wmsInventoryCountTask);


    }

    private void updateStockCheckStock(WmsStockdetail stockDetail, WmsLocate wmsLocate, WmsInventoryCountTaskVo wmsInventoryCountTaskVo) {
        // 盘点数量相等且不是立库的盘点，更新库存状态
        if (wmsInventoryCountTaskVo.getCountedQuantity().equals(stockDetail.getQuantity()) &&
                !StringUtils.isEmpty(wmsLocate.getLkLocate()) &&
                !wmsLocate.getLkLocate().equals(WmsConstant.WhetherEnum.YES.getValue())) {
            stockDetail.setInvState(InvStateEnum.ZHENGCHANG.getCode());
            // 对库存状态更新
            wmsStockdetailMapper.updateById(stockDetail);
        }
    }

    private void updateStockCheckStatus(WmsInventoryCountTask wmsInventoryCountTask) {
        String planId = wmsInventoryCountTask.getPlanId();
        WmsInventoryCountPlan inventoryCountPlan = wmsInventoryCountPlanMapper.selectById(planId);
        List<WmsInventoryCountTask> inventoryCountTasks = this.list(new QueryWrapper<WmsInventoryCountTask>().eq("plan_id", planId));
        List<String> taskStatusList = inventoryCountTasks.stream().map(WmsInventoryCountTask::getTaskStatus).distinct().collect(Collectors.toList());
        if(taskStatusList.size() == WmsConstant.INT_ONE) {
            String taskStatus = taskStatusList.get(0);
            if (CountTaskStatusEnum.PANDIANCHUANGJIAN.getCode().equals(taskStatus)) {
                inventoryCountPlan.setStatus(InventoryCountPlanStatusEnum.RENGWUSHENGCHENG.getCode());
            } else {
                inventoryCountPlan.setStatus(InventoryCountPlanStatusEnum.PANDIANWANCHENG.getCode());
            }
        } else {
            inventoryCountPlan.setStatus(InventoryCountPlanStatusEnum.BUFENWANCHENG.getCode());
        }
        wmsInventoryCountPlanMapper.updateById(inventoryCountPlan);
    }

    /**
     * 更新盘点任务信息
     * @param wmsInventoryCountTaskVo
     * @param wmsInventoryCountTask
     * @param stockdetail
     */
    private void updateStockCheckTask(WmsInventoryCountTaskVo wmsInventoryCountTaskVo, WmsInventoryCountTask wmsInventoryCountTask, WmsStockdetail stockDetail) {
        wmsInventoryCountTask.setTaskStatus(CountTaskStatusEnum.PANDIANWANCHENG.getCode());
        wmsInventoryCountTask.setCountedQuantity(wmsInventoryCountTaskVo.getCountedQuantity());
        wmsInventoryCountTask.setDifferenceQuantity(wmsInventoryCountTaskVo.getCountedQuantity() - stockDetail.getQuantity());
        this.updateById(wmsInventoryCountTask);
    }



    /**
     * 验证盘点任务
     * @param wmsInventoryCountTaskVo
     * @return
     */
    private WmsInventoryCountTask validStockCheckTask(WmsInventoryCountTaskVo wmsInventoryCountTaskVo) {
        QueryWrapper<WmsInventoryCountTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(WmsInventoryCountTask::getTaskCode, wmsInventoryCountTaskVo.getTaskCode())
                .or().eq(WmsInventoryCountTask::getLpn, wmsInventoryCountTaskVo.getLpn());
        List<WmsInventoryCountTask> stockCheckTasks = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(stockCheckTasks)) {
            throw new JeecgBootException("盘点任务不存在");
        }

        if (stockCheckTasks.size() > 1) {
            throw new JeecgBootException("当前含有多个盘点任务，请核实");
        }

        boolean noCreate = stockCheckTasks.stream().anyMatch(stockCheckTask -> !CountTaskStatusEnum.PANDIANCHUANGJIAN.getCode().equals(stockCheckTask.getTaskStatus()));
        if (noCreate) {
            throw new JeecgBootException("PDA盘点任务须为：盘点创建状态");
        }
        return stockCheckTasks.get(WmsConstant.INT_ZERO);

    }

    private WmsStockdetail validStockCheckStock(WmsInventoryCountTaskVo wmsInventoryCountTaskVo) {
        QueryWrapper<WmsStockdetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(WmsStockdetail::getItemCode, wmsInventoryCountTaskVo.getItemCode())
                .eq(WmsStockdetail::getBatchCode, wmsInventoryCountTaskVo.getBatchCode())
                .eq(WmsStockdetail::getLpn, wmsInventoryCountTaskVo.getLpn())
                .eq(WmsStockdetail::getLocateCode, wmsInventoryCountTaskVo.getLocateCode());
        List<WmsStockdetail> stockDetails = wmsStockdetailMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(stockDetails)) {
            throw new JeecgBootException("库存信息不存在");
        }
        if (stockDetails.size() > WmsConstant.INT_ONE) {
            throw new JeecgBootException("库存信息不唯一");
        }
        return stockDetails.get(WmsConstant.INT_ZERO);
    }

    private void validStockCheckReq(WmsInventoryCountTaskVo wmsInventoryCountTaskVo) {
        if (StringUtils.isEmpty(wmsInventoryCountTaskVo.getItemCode())) {
            throw new JeecgBootException("物料编号不能为空");
        }

        if (StringUtils.isEmpty(wmsInventoryCountTaskVo.getBatchCode())) {
            throw new JeecgBootException("盘点批号不能为空");
        }

        if (wmsInventoryCountTaskVo.getCountedQuantity() == null || wmsInventoryCountTaskVo.getCountedQuantity() < 0) {
            throw new JeecgBootException("请输入正确的盘点数量");
        }
    }
/**
     * 通过盘点方案id查询盘点任务
     * @param planId
     * @return
     */
    @Override
    public List<WmsInventoryCountTask> getCountTaskByPlanId(String planId) {
        return this.list(new QueryWrapper<WmsInventoryCountTask>().eq("plan_id", planId));
    }
    
}
