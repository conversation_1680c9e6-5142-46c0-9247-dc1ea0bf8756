package org.jeecg.modules.admin.service.impl;

import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.admin.entity.WmsFeatureConfiguration;
import org.jeecg.modules.admin.mapper.WmsFeatureConfigurationMapper;
import org.jeecg.modules.admin.service.IWmsFeatureConfigurationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 功能配置
 * @Author: jeecg-boot
 * @Date:   2024-07-11
 * @Version: V1.0
 */
@Service
public class WmsFeatureConfigurationServiceImpl extends ServiceImpl<WmsFeatureConfigurationMapper, WmsFeatureConfiguration> implements IWmsFeatureConfigurationService {
    @Autowired
    private WmsFeatureConfigurationMapper wmsFeatureConfigurationMapper;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveMain(List<WmsFeatureConfiguration> wmsFeatureConfigurationList) {
// 1. 先查出数据库已有的所有数据(也可改为只查与入参 field 相关的数据)
        List<WmsFeatureConfiguration> existingList = wmsFeatureConfigurationMapper.selectList(null);

        // 2. 构建 Map, 便于判断
        Map<String, WmsFeatureConfiguration> existingMap = existingList.stream()
                .collect(Collectors.toMap(WmsFeatureConfiguration::getField,
                        Function.identity(),
                        (oldValue, newValue) -> oldValue));

        // 3. 收集需要删除的记录ID
        List<String> idsToDelete = new ArrayList<>();
        // 这里可以先去重一下 wmsFeatureConfigurationList，以防前端传参 itself 就有重复 field
        List<WmsFeatureConfiguration> distinctIncoming = wmsFeatureConfigurationList.stream()
                .collect(Collectors.toMap(WmsFeatureConfiguration::getField, Function.identity(), (oldValue, newValue) -> newValue))
                .values().stream().collect(Collectors.toList());

        for (WmsFeatureConfiguration cfg : distinctIncoming) {
            if (existingMap.containsKey(cfg.getField())) {
                idsToDelete.add(existingMap.get(cfg.getField()).getId());
            }
        }

        // 4. 删除数据库中原有的重复数据
        if (!idsToDelete.isEmpty()) {
            wmsFeatureConfigurationMapper.deleteBatchIds(idsToDelete);
        }

        // 5. 此时做一次【再次查询】或在插入时利用数据库的唯一索引来防止重复
        // 这里为了演示再次查询, 假如是高并发场景, 建议用唯一索引+捕获异常的方式
        List<WmsFeatureConfiguration> newExistingList = wmsFeatureConfigurationMapper.selectList(null);
        Set<String> newExistingFields = newExistingList.stream()
                .map(WmsFeatureConfiguration::getField)
                .collect(Collectors.toSet());

        // 6. 过滤掉二次查完后依然已经存在的 field, 避免并发插入
        List<WmsFeatureConfiguration> finalInsertList = distinctIncoming.stream()
                .filter(cfg -> !newExistingFields.contains(cfg.getField()))
                .collect(Collectors.toList());
        if (finalInsertList.isEmpty()) {
           throw new RuntimeException("无有效数据插入");
        }

        // 7. 插入新的数据(底层也是批量插入)
       this.saveBatch(finalInsertList);
    }
}
