package org.jeecg.modules.admin.service;

import org.jeecg.modules.admin.entity.WmsPurchaseDetail;
import org.jeecg.modules.admin.entity.WmsPurchase;
import com.baomidou.mybatisplus.extension.service.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 采购订单
 * @Author: jeecg-boot
 * @Date:   2024-10-26
 * @Version: V1.0
 */
public interface IWmsPurchaseService extends IService<WmsPurchase> {

	/**
	 * 添加一对多
	 *
	 * @param wmsPurchase
	 * @param wmsPurchaseDetailList
	 */
	public void saveMain(WmsPurchase wmsPurchase,List<WmsPurchaseDetail> wmsPurchaseDetailList) ;
	
	/**
	 * 修改一对多
	 *
	 * @param wmsPurchase
	 * @param wmsPurchaseDetailList
	 */
	public void updateMain(WmsPurchase wmsPurchase,List<WmsPurchaseDetail> wmsPurchaseDetailList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);
	
}
