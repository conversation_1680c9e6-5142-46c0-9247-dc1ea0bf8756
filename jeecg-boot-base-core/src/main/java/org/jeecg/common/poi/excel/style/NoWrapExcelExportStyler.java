package org.jeecg.common.poi.excel.style;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.export.styler.AbstractExcelExportStyler;
import org.jeecgframework.poi.excel.export.styler.IExcelExportStyler;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;

import java.util.HashMap;
import java.util.Map;

/**
 * 自定义Excel导出样式，设置单元格不换行
 */
public class NoWrapExcelExportStyler extends AbstractExcelExportStyler implements IExcelExportStyler {

    // 样式缓存，用于复用样式
    private Map<String, CellStyle> styleCache = new HashMap<String, CellStyle>();
    // 字体缓存
    private Map<String, Font> fontCache = new HashMap<String, Font>();

    public NoWrapExcelExportStyler(Workbook workbook) {
        super.createStyles(workbook);
    }

    @Override
    public CellStyle getHeaderStyle(short headerColor) {
        String key = "header_" + headerColor;
        CellStyle style = styleCache.get(key);
        if (style == null) {
            style = getBaseCellStyle();
            style.setAlignment(HorizontalAlignment.CENTER);
            style.setVerticalAlignment(VerticalAlignment.CENTER);
            Font font = getFont(true, (short) 12);
            style.setFont(font);
            style.setWrapText(false); // 关键：设置文本不换行
            styleCache.put(key, style);
        }
        return style;
    }

    @Override
    public CellStyle getTitleStyle(short color) {
        String key = "title_" + color;
        CellStyle style = styleCache.get(key);
        if (style == null) {
            style = getBaseCellStyle();
            style.setAlignment(HorizontalAlignment.CENTER);
            style.setVerticalAlignment(VerticalAlignment.CENTER);
            Font font = getFont(true, (short) 12);
            style.setFont(font);
            style.setWrapText(false); // 关键：设置文本不换行
            styleCache.put(key, style);
        }
        return style;
    }

    @Override
    public CellStyle getStyles(boolean parity, ExcelExportEntity entity) {
        String key = "data_" + parity;
        CellStyle style = styleCache.get(key);
        if (style == null) {
            style = getBaseCellStyle();
            style.setAlignment(HorizontalAlignment.CENTER);
            style.setVerticalAlignment(VerticalAlignment.CENTER);
            Font font = getFont(false, (short) 11); // 内容使用非加粗字体，字号设为11
            style.setFont(font);
            style.setWrapText(false); // 关键：设置文本不换行
            styleCache.put(key, style);
        }
        return style;
    }

    /**
     * 基础样式 - 使用缓存获取基础样式
     */
    private CellStyle getBaseCellStyle() {
        String key = "base_style";
        CellStyle style = styleCache.get(key);
        if (style == null) {
            style = workbook.createCellStyle();
            style.setBorderBottom(BorderStyle.THIN);
            style.setBorderLeft(BorderStyle.THIN);
            style.setBorderRight(BorderStyle.THIN);
            style.setBorderTop(BorderStyle.THIN);
            styleCache.put(key, style);
        }
        return style;
    }

    /**
     * 获取字体，支持缓存复用
     * @param bold 是否加粗
     * @param size 字体大小
     * @return Font对象
     */
    private Font getFont(boolean bold, short size) {
        String key = "font_" + bold + "_" + size;
        Font font = fontCache.get(key);
        if (font == null) {
            font = workbook.createFont();
            font.setFontHeightInPoints(size);
            font.setBold(bold);
            fontCache.put(key, font);
        }
        return font;
    }
} 