package org.jeecg.modules.quartz.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jeecg.common.api.dto.message.MessageDTO;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.ComboModel;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.util.DateUtils;
import org.jeecg.modules.admin.entity.WmsAgvWarn;
import org.jeecg.modules.admin.entity.WmsFeatureConfiguration;
import org.jeecg.modules.admin.service.IWmsAgvWarnService;
import org.jeecg.modules.admin.service.IWmsFeatureConfigurationService;
import org.jeecg.modules.admin.util.HttpClientUtil;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 设备预警定时任务
 * 每天凌晨6点执行一次，检查设备状态并发送预警信息
 *
 * @date: 2024-11-05
 */
@PersistJobDataAfterExecution
@DisallowConcurrentExecution
@Slf4j
@Component
public class EquipmentAlarmJob implements Job {
    @Autowired
    private ISysBaseAPI sysBaseAPI;

    @Autowired
    private IWmsFeatureConfigurationService wmsFeatureConfigurationService;

    @Autowired
    private IWmsAgvWarnService wmsAgvWarnService;

    /**
     * WCS设备告警接口URL配置字段名
     */
    private static final String WCS_ALARM_URL_FIELD = "wcsRbsAlarmQueryUrl";

    /**
     * WCS设备告警查询起始时间配置字段名
     */
    private static final String WCS_ALARM_START_TIME_FIELD = "wcsRbsAlarmQueryStartTime";

    /**
     * 系统消息发送人
     */
    private static final String SYSTEM_SENDER = "system";

    /**
     * 数据字典编码，用于翻译告警信息
     */
    private static final String ALARM_DICT_CODE = "four_way_car_alarm";

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        log.info("--- 设备预警定时任务开始执行 时间: {} ---", DateUtils.now());
        // 获取查询起始时间
        String startTime = getQueryStartTime();
        String currentTime = getCurrentTimeFormatted();
        try {
            // 调用WCS接口查询设备告警信息
            JSONObject alarmData = queryEquipmentAlarms(startTime,currentTime);

            // 处理告警信息并发送系统消息
            if (alarmData == null) {
                log.error("获取设备告警信息失败，请检查功能配置和网络连接");
            } else if (alarmData.getInteger("code") == 1) {
                processAlarmData(alarmData);
            } else {
                log.warn("获取设备告警信息失败，响应码非1: {}", alarmData.toJSONString());
            }

            // 查询AGV告警信息并发送通知
            processAgvWarnings(startTime, currentTime);

            updateQueryStartTime(currentTime);

        } catch (Exception e) {
            log.error("设备预警定时任务执行异常", e);
            throw new JobExecutionException(e);
        }
        log.info("--- 设备预警定时任务执行完毕 时间: {} ---", DateUtils.now());
    }

    /**
     * 查询设备告警信息
     * @return 告警信息JSON对象
     */
    private JSONObject queryEquipmentAlarms(String startTime,String currentTime ) {
        // 从功能配置中获取WCS告警查询URL
        String wcsAlarmUrl = getWcsAlarmUrl();
        if (wcsAlarmUrl == null || wcsAlarmUrl.isEmpty()) {
            log.error("未配置WCS设备告警接口URL，请在功能配置中设置{}", WCS_ALARM_URL_FIELD);
            return null;
        }

        // 构建请求参数
        JSONObject requestBody = new JSONObject();
        requestBody.put("pageNum", 1);
        requestBody.put("pageSize", 20);

        // 构建查询条件
        JSONObject query = new JSONObject();
        query.put("defineId", null);
        query.put("detail", null);
        query.put("type", "VEHICLE_ERR");
        query.put("createTime", startTime);  // 设置起始时间
        query.put("overTime", currentTime);  // 设置结束时间为当前时间
        requestBody.put("query", query);

        log.info("发送设备告警查询请求: URL={}, 时间范围={} 至 {}, 参数={}",
                wcsAlarmUrl, startTime, currentTime, requestBody.toJSONString());

        // 判断是否使用模拟数据进行测试
        boolean useTestMode = isTestMode();
        JSONObject result;

        if (useTestMode) {
            // 使用模拟数据进行测试
            log.info("使用模拟数据进行测试");
            result = createMockResponse(startTime, currentTime);
        } else {
            // 创建HttpClient实例
            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                // 创建HttpPost请求
                HttpPost httpPost = new HttpPost(wcsAlarmUrl);
                httpPost.setHeader("Content-Type", "application/json");
                // 添加userName请求头
                httpPost.setHeader("userName", "wms");

                // 构建请求体
                String json = JSON.toJSONString(requestBody);
                StringEntity entity = new StringEntity(json, "UTF-8");
                httpPost.setEntity(entity);

                // 执行请求
                try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                    // 检查响应状态
                    int statusCode = response.getStatusLine().getStatusCode();
                    String responseBody = EntityUtils.toString(response.getEntity(), "UTF-8");
                    if (statusCode >= 200 && statusCode < 300) {
                        result = JSON.parseObject(responseBody);
                    } else {
                        log.error("请求失败，状态码：{}，响应：{}", statusCode, responseBody);
                        result = null;
                    }
                }
            } catch (Exception e) {
                log.error("发送请求异常", e);
                result = null;
            }
        }

        return result;
    }

    /**
     * 检查是否处于测试模式
     * @return 是否使用测试模式
     */
    private boolean isTestMode() {
       return false ;
    }

    /**
     * 创建模拟的响应数据
     * @param startTime 查询起始时间
     * @param endTime 查询结束时间
     * @return 模拟的响应数据
     */
    private JSONObject createMockResponse(String startTime, String endTime) {
        JSONObject response = new JSONObject();
        response.put("code", 1);  // 成功状态码

        // 创建数据部分
        JSONObject data = new JSONObject();
        data.put("currentPage", 1);
        data.put("pageSize", 20);

        // 创建告警列表
        JSONArray resultList = new JSONArray();

        // 添加模拟的告警数据（生成两条测试数据）
        resultList.add(createMockAlarmItem("*************", "16-220", getCurrentTimeFormatted(), "64", ""));
        resultList.add(createMockAlarmItem("*************", "16-204", getCurrentTimeFormatted(), "65", ""));

        data.put("result", resultList);
        response.put("data", data);

        log.info("生成模拟数据: {}", response.toJSONString());
        return response;
    }

    /**
     * 创建单条模拟告警数据
     * @param ip 设备IP
     * @param alarmCode 告警编号
     * @param alarmTime 告警时间
     * @param deviceCode 设备编码
     * @param warnMessage 告警描述（不使用，会从字典中获取）
     * @return 模拟的告警数据
     */
    private JSONObject createMockAlarmItem(String ip, String alarmCode, String alarmTime, String deviceCode, String warnMessage) {
        JSONObject alarm = new JSONObject();
        alarm.put("createTime", alarmTime);
        alarm.put("defineId", ip);
        alarm.put("detail", alarmCode);
        alarm.put("id", null);
        alarm.put("type", "VEHICLE_ERR");

        // 创建消息详情
        JSONObject message = new JSONObject();
        message.put("deviceCode", deviceCode);
        message.put("ip", ip);
        message.put("warnNo", alarmCode);
        message.put("warnMessage", "");  // 留空，模拟实际情况，由字典翻译
        message.put("warnTime", alarmTime);

        alarm.put("message", message);
        return alarm;
    }

    /**
     * 从功能配置中获取WCS告警查询URL
     * @return WCS告警查询URL
     */
    private String getWcsAlarmUrl() {
        try {
            // 构建查询条件
            QueryWrapper<WmsFeatureConfiguration> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("field", WCS_ALARM_URL_FIELD);

            // 查询配置
            WmsFeatureConfiguration config = wmsFeatureConfigurationService.getOne(queryWrapper);

            if (config != null && config.getValue() != null && !config.getValue().isEmpty()) {
                return config.getValue().trim();
            } else {
                log.warn("未找到WCS设备告警接口URL配置，请在功能配置中设置{}", WCS_ALARM_URL_FIELD);
                return null;
            }
        } catch (Exception e) {
            log.error("获取WCS设备告警接口URL配置异常", e);
            return null;
        }
    }

    /**
     * 获取查询起始时间
     * @return 格式化后的查询起始时间，格式为"yyyyMMddHHmmss"
     */
    private String getQueryStartTime() {
        try {
            // 构建查询条件
            QueryWrapper<WmsFeatureConfiguration> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("field", WCS_ALARM_START_TIME_FIELD);

            // 查询配置
            WmsFeatureConfiguration config = wmsFeatureConfigurationService.getOne(queryWrapper);

            if (config != null && config.getValue() != null && !config.getValue().isEmpty()) {
                // 将数据库中的时间格式 "yyyy-MM-dd HH:mm:ss" 转换为 "yyyyMMddHHmmss"
                return convertTimeFormat(config.getValue().trim());
            } else {
                // 如果没有配置，使用当前时间前24小时作为默认起始时间
                String defaultStartTime = getDefaultStartTime();
                log.warn("未找到查询起始时间配置，使用默认时间: {}", defaultStartTime);
                return defaultStartTime;
            }
        } catch (Exception e) {
            log.error("获取查询起始时间配置异常", e);
            // 发生异常时使用当前时间前24小时作为默认起始时间
            return getDefaultStartTime();
        }
    }

    /**
     * 获取默认的查询起始时间（当前时间前24小时）
     * @return 格式化后的默认起始时间，格式为"yyyyMMddHHmmss"
     */
    private String getDefaultStartTime() {
        try {
            // 获取当前时间
            Date now = new Date();
            // 计算前24小时
            Date oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            // 格式化
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            return sdf.format(oneDayAgo);
        } catch (Exception e) {
            log.error("获取默认起始时间异常", e);
            // 如果发生异常，返回空字符串
            return "";
        }
    }

    /**
     * 获取当前时间并格式化为"yyyyMMddHHmmss"
     * @return 格式化后的当前时间
     */
    private String getCurrentTimeFormatted() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        return sdf.format(new Date());
    }

    /**
     * 将数据库中的时间格式 "yyyy-MM-dd HH:mm:ss" 转换为 "yyyyMMddHHmmss"
     * @param dbTimeStr 数据库中的时间字符串
     * @return 转换后的时间字符串
     */
    private String convertTimeFormat(String dbTimeStr) {
        try {
            if (dbTimeStr == null || dbTimeStr.isEmpty()) {
                return getDefaultStartTime();
            }

            // 如果已经是目标格式，直接返回
            if (dbTimeStr.length() == 14 && !dbTimeStr.contains("-") && !dbTimeStr.contains(":")) {
                return dbTimeStr;
            }

            // 解析数据库时间格式
            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = inputFormat.parse(dbTimeStr);

            // 转换为目标格式
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            return outputFormat.format(date);
        } catch (Exception e) {
            log.error("转换时间格式异常: {}", dbTimeStr, e);
            return getDefaultStartTime();
        }
    }

    /**
     * 更新查询起始时间配置
     * @param currentTimeForWcs 当前时间（WCS格式）
     */
    private void updateQueryStartTime(String currentTimeForWcs) {
        try {
            // 将WCS格式的时间转换为数据库格式
            String dbTimeFormat = convertToDbTimeFormat(currentTimeForWcs);

            // 构建查询条件
            QueryWrapper<WmsFeatureConfiguration> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("field", WCS_ALARM_START_TIME_FIELD);

            // 查询配置
            WmsFeatureConfiguration config = wmsFeatureConfigurationService.getOne(queryWrapper);

            if (config != null) {
                // 更新配置
                config.setValue(dbTimeFormat);
                wmsFeatureConfigurationService.updateById(config);
                log.info("已更新查询起始时间配置为: {}", dbTimeFormat);
            } else {
                // 创建新配置
                config = new WmsFeatureConfiguration();
                config.setField(WCS_ALARM_START_TIME_FIELD);
                config.setValue(dbTimeFormat);
                wmsFeatureConfigurationService.save(config);
                log.info("已创建查询起始时间配置: {}", dbTimeFormat);
            }
        } catch (Exception e) {
            log.error("更新查询起始时间配置异常", e);
        }
    }

    /**
     * 将WCS格式的时间 "yyyyMMddHHmmss" 转换为数据库格式 "yyyy-MM-dd HH:mm:ss"
     * @param wcsTimeStr WCS格式的时间字符串
     * @return 数据库格式的时间字符串
     */
    private String convertToDbTimeFormat(String wcsTimeStr) {
        try {
            if (wcsTimeStr == null || wcsTimeStr.isEmpty() || wcsTimeStr.length() != 14) {
                return DateUtils.now();
            }

            // 解析WCS时间格式
            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            Date date = inputFormat.parse(wcsTimeStr);

            // 转换为数据库格式
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return outputFormat.format(date);
        } catch (Exception e) {
            log.error("转换为数据库时间格式异常: {}", wcsTimeStr, e);
            return DateUtils.now();
        }
    }

    /**
     * 处理告警数据并发送系统消息
     * @param alarmData 告警数据JSON对象
     */
    private void processAlarmData(JSONObject alarmData) {
        JSONObject data = alarmData.getJSONObject("data");
        if (data == null) {
            log.warn("告警数据为空");
            return;
        }

        JSONArray alarmList = data.getJSONArray("result");
        if (alarmList == null || alarmList.isEmpty()) {
            log.info("无设备告警信息");
            return;
        }

        log.info("获取到{}条设备告警信息", alarmList.size());

        // 遍历告警信息并发送系统消息
        for (int i = 0; i < alarmList.size(); i++) {
            JSONObject alarm = alarmList.getJSONObject(i);
            sendAlarmMessage(alarm);
        }
    }



    /**
     * 发送告警消息
     * @param alarm 单条告警信息
     */
    private void sendAlarmMessage(JSONObject alarm) {
        try {
            // 获取告警详情
            String deviceId = alarm.getString("defineId");
            String alarmCode = alarm.getString("detail");
            String createTime = alarm.getString("createTime");

            // 获取消息详情
            JSONObject message = alarm.getJSONObject("message");
            String deviceCode = message != null ? message.getString("deviceCode") : "";
            String ip = message != null ? message.getString("ip") : deviceId;
            String warnNo = message != null ? message.getString("warnNo") : alarmCode;
            String warnTime = message != null ? message.getString("warnTime") : createTime;

            // 从数据字典中获取告警描述
            String warnMessage = getAlarmMessageFromDict(warnNo);

            // 格式化时间
            String formattedTime = formatAlarmTime(warnTime);

            // 构建消息标题和内容
            String title = "设备告警通知: " + deviceCode + "(" + ip + ")";
            StringBuilder content = new StringBuilder();
            content.append("<h3>设备告警详情</h3>");
            content.append("<p><strong>设备编码:</strong> ").append(deviceCode).append("</p>");
            content.append("<p><strong>设备IP:</strong> ").append(ip).append("</p>");
            content.append("<p><strong>告警编号:</strong> ").append(warnNo).append("</p>");
            content.append("<p><strong>告警时间:</strong> ").append(formattedTime).append("</p>");

            if (warnMessage != null && !warnMessage.isEmpty()) {
                content.append("<p><strong>告警描述:</strong> ").append(warnMessage).append("</p>");
            } else {
                content.append("<p><strong>告警描述:</strong> 未知告警</p>");
            }

            // 获取所有活跃用户
            List<ComboModel> allUsers = sysBaseAPI.queryAllUserBackCombo();
            if (allUsers != null && !allUsers.isEmpty()) {
                // 提取所有用户名并用逗号连接
                String usernames = allUsers.stream()
                    .map(ComboModel::getUsername)
                    .collect(Collectors.joining(","));

                // 创建消息对象
                MessageDTO messageDTO = new MessageDTO(SYSTEM_SENDER, usernames, title, content.toString(), CommonConstant.MSG_CATEGORY_2);

                // 发送系统消息
                sysBaseAPI.sendSysAnnouncement(messageDTO);
                log.info("已发送设备告警消息给所有用户: {}, 告警描述: {}", title, warnMessage);
            } else {
                log.warn("未找到活跃用户，无法发送设备告警消息");
            }
        } catch (Exception e) {
            log.error("发送设备告警消息失败", e);
        }
    }

    /**
     * 从数据字典中获取告警描述
     * @param alarmCode 告警编号
     * @return 告警描述
     */
    private String getAlarmMessageFromDict(String alarmCode) {
        try {
            if (alarmCode == null || alarmCode.isEmpty()) {
                return "";
            }

            // 从数据字典中获取告警信息
            List<DictModel> dictItems = sysBaseAPI.getDictItems(ALARM_DICT_CODE);
            if (dictItems == null || dictItems.isEmpty()) {
                log.warn("未找到告警信息字典: {}", ALARM_DICT_CODE);
                return "";
            }

            // 遍历字典项，查找匹配的告警编号
            for (DictModel dictItem : dictItems) {
                if (alarmCode.equals(dictItem.getValue())) {
                    return dictItem.getText();
                }
            }

            log.warn("在字典中未找到告警编号对应的描述: {}", alarmCode);
            return "";
        } catch (Exception e) {
            log.error("从字典获取告警描述异常: {}", alarmCode, e);
            return "";
        }
    }

    /**
     * 格式化告警时间
     * @param timeStr 原始时间字符串 (格式: yyyyMMddHHmmss)
     * @return 格式化后的时间字符串
     */
    private String formatAlarmTime(String timeStr) {
        if (timeStr == null || timeStr.length() != 14) {
            return DateUtils.now();
        }

        try {
            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = inputFormat.parse(timeStr);
            return outputFormat.format(date);
        } catch (Exception e) {
            log.error("格式化告警时间失败", e);
            return DateUtils.now();
        }
    }

    /**
     * 处理AGV告警信息并发送系统消息
     */
    private void processAgvWarnings(String startTime,String currentTime) {
        try {
            QueryWrapper<WmsAgvWarn> queryWrapper = new QueryWrapper<>();
            queryWrapper.between("create_time",startTime,currentTime)
                    .orderByAsc("create_time");

            // 查询未处理的AGV告警信息
            List<WmsAgvWarn> agvWarnings = wmsAgvWarnService.list(queryWrapper);

            if (agvWarnings == null || agvWarnings.isEmpty()) {
                log.info("无AGV告警信息");
                return;
            }

            log.info("获取到{}条AGV告警信息", agvWarnings.size());

            // 遍历告警信息并发送系统消息
            for (WmsAgvWarn agvWarn : agvWarnings) {
                sendAgvWarnMessage(agvWarn);
            }
        } catch (Exception e) {
            log.error("处理AGV告警信息异常", e);
        }
    }

    /**
     * 发送AGV告警消息
     * @param agvWarn AGV告警信息
     */
    private void sendAgvWarnMessage(WmsAgvWarn agvWarn) {
        try {
            // 格式化时间
            String formattedTime = agvWarn.getBeginDate();
            if (formattedTime != null && formattedTime.length() == 14) {
                formattedTime = formatAlarmTime(formattedTime);
            }

            // 构建消息标题和内容
            String title = "AGV告警通知: " + agvWarn.getRobotCode();
            StringBuilder content = new StringBuilder();
            content.append("<h3>AGV告警详情</h3>");
            content.append("<p><strong>客户端编号:</strong> ").append(agvWarn.getClientCode()).append("</p>");
            content.append("<p><strong>小车编号:</strong> ").append(agvWarn.getRobotCode()).append("</p>");
            content.append("<p><strong>告警时间:</strong> ").append(formattedTime).append("</p>");
            content.append("<p><strong>告警内容:</strong> ").append(agvWarn.getWarnContent()).append("</p>");

            if (agvWarn.getTaskCode() != null && !agvWarn.getTaskCode().isEmpty()) {
                content.append("<p><strong>任务编号:</strong> ").append(agvWarn.getTaskCode()).append("</p>");
            }

            // 获取所有活跃用户
            List<ComboModel> allUsers = sysBaseAPI.queryAllUserBackCombo();
            if (allUsers != null && !allUsers.isEmpty()) {
                // 提取所有用户名并用逗号连接
                String usernames = allUsers.stream()
                    .map(ComboModel::getUsername)
                    .collect(Collectors.joining(","));

                // 创建消息对象
                MessageDTO messageDTO = new MessageDTO(SYSTEM_SENDER, usernames, title, content.toString(), CommonConstant.MSG_CATEGORY_2);

                // 发送系统消息
                sysBaseAPI.sendSysAnnouncement(messageDTO);
                log.info("已发送AGV告警消息给所有用户: {}, 告警内容: {}", title, agvWarn.getWarnContent());
            } else {
                log.warn("未找到活跃用户，无法发送AGV告警消息");
            }
        } catch (Exception e) {
            log.error("发送AGV告警消息失败", e);
        }
    }
}