package org.jeecg.modules.quartz.job;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.encoder.PatternLayoutEncoder;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.AppenderBase;
import ch.qos.logback.core.OutputStreamAppender;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 内存日志Appender
 * 用于捕获SLF4J输出的日志到内存中
 */
@Slf4j
public class MemoryAppender extends OutputStreamAppender<ILoggingEvent> {
    
    // 存储任务ID和对应的日志输出流的映射
    private static final ConcurrentHashMap<String, ByteArrayOutputStream> TASK_LOG_MAP = new ConcurrentHashMap<>();
    
    // 存储已添加的Appender名称，用于后续移除
    private static final List<String> APPENDERS = new ArrayList<>();
    
    private String taskId;
    private Level threshold = Level.ALL; // 捕获所有级别的日志，从INFO改为ALL
    
    /**
     * 为特定任务初始化日志捕获
     * @param taskId 任务ID
     * @param outputStream 日志输出流
     */
    public static void initializeForTask(String taskId, ByteArrayOutputStream outputStream) {
        try {
            TASK_LOG_MAP.put(taskId, outputStream);
            
            // 获取根Logger
            LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
            Logger rootLogger = loggerContext.getLogger(Logger.ROOT_LOGGER_NAME);
            
            // 创建并配置Appender
            MemoryAppender memoryAppender = new MemoryAppender();
            memoryAppender.setContext(loggerContext);
            memoryAppender.setTaskId(taskId);
            memoryAppender.setThreshold(Level.ALL); // 显式设置为捕获所有级别
            
            // 设置encoder
            PatternLayoutEncoder encoder = new PatternLayoutEncoder();
            encoder.setContext(loggerContext);
            encoder.setPattern("%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36}:%line - %msg%n");
            encoder.start();
            
            memoryAppender.setEncoder(encoder);
            memoryAppender.start();
            
            // 添加到根Logger
            rootLogger.addAppender(memoryAppender);
            
            // 记录Appender名称
            String appenderName = "MemoryAppender-" + taskId;
            APPENDERS.add(appenderName);
            
            log.info("为任务 {} 初始化日志捕获", taskId);
        } catch (Exception e) {
            log.error("初始化日志捕获失败", e);
        }
    }
    
    /**
     * 清理任务的日志捕获
     * @param taskId 任务ID
     */
    public static void cleanupForTask(String taskId) {
        try {
            // 移除Appender
            LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
            Logger rootLogger = loggerContext.getLogger(Logger.ROOT_LOGGER_NAME);
            
            String appenderName = "MemoryAppender-" + taskId;
            if (APPENDERS.contains(appenderName)) {
                rootLogger.detachAppender(appenderName);
                APPENDERS.remove(appenderName);
            }
            
            // 移除任务日志流
            TASK_LOG_MAP.remove(taskId);
            
            log.info("清理任务 {} 的日志捕获", taskId);
        } catch (Exception e) {
            log.error("清理日志捕获失败", e);
        }
    }
    
    public void setTaskId(String taskId) {
        this.taskId = taskId;
        setName("MemoryAppender-" + taskId);
    }
    
    public void setThreshold(Level threshold) {
        this.threshold = threshold;
    }
    
    @Override
    public void start() {
        ByteArrayOutputStream outputStream = TASK_LOG_MAP.get(taskId);
        if (outputStream != null) {
            setOutputStream(outputStream);
            super.start();
        } else {
            log.error("无法启动MemoryAppender，未找到任务ID: {}", taskId);
        }
    }
    
    @Override
    protected void append(ILoggingEvent eventObject) {
        if (!isStarted()) {
            return;
        }
        
        // 根据日志级别过滤
        if (eventObject.getLevel().isGreaterOrEqual(threshold)) {
            super.append(eventObject);
        }
    }
} 