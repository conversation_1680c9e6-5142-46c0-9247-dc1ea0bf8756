package org.jeecg.modules.quartz.job;

import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;

/**
 * 示例任务，用于测试日志捕获功能
 */
@Slf4j
public class SampleJob extends BaseQuartzJob {
    
    @Override
    protected void executeTask(JobExecutionContext context) throws Exception {
        // 获取任务参数
        String parameter = context.getJobDetail().getJobDataMap().getString("parameter");
        
        // 输出一些日志信息
        System.out.println("这是一个示例任务，演示日志捕获功能");
        System.out.println("任务参数: " + parameter);
        
        // 使用SLF4J记录日志
        log.info("这是使用SLF4J记录的INFO级别日志");
        log.warn("这是使用SLF4J记录的WARN级别日志");
        log.debug("这是使用SLF4J记录的DEBUG级别日志");
        
        log.info("任务开始模拟数据处理");
        
        // 模拟任务执行
        for (int i = 1; i <= 5; i++) {
            // 使用System.out.println
            System.out.println("步骤 " + i + "/5: 处理中...");
            
            // 使用SLF4J记录日志
            log.info("第{}页处理完成，成功：{}，失败：{}", i, i*2, i-1);
            
            // 模拟处理延迟
            Thread.sleep(1000);
        }
        
        // 模拟一些业务日志
        log.info("数据处理汇总结果: 总数据量:{}, 处理成功:{}, 处理失败:{}", 100, 95, 5);
        
        // 输出一些错误日志
        System.err.println("这是使用System.err输出的错误日志");
        log.error("这是使用SLF4J输出的ERROR级别日志");
        
        try {
            // 模拟业务异常，但会捕获
            if ("error".equals(parameter)) {
                throw new RuntimeException("模拟业务异常");
            }
        } catch (Exception e) {
            // 使用SLF4J记录异常
            log.error("处理数据时遇到错误", e);
        }
        
        // 任务结束
        log.info("示例任务执行完成，所有数据处理结束");
    }
}
