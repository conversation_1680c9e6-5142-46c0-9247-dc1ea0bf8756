package org.jeecg.modules.quartz.job;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.quartz.service.impl.QuartzJobServiceImpl;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.io.OutputStream;

/**
 * 定时任务日志捕获增强器
 * 用于捕获控制台输出日志
 */
@Slf4j
public class LogCaptureEnhancer {
    
    // 保存原始System.out
    private static final PrintStream ORIGINAL_STDOUT = System.out;
    // 保存原始System.err
    private static final PrintStream ORIGINAL_STDERR = System.err;
    
    /**
     * 开始捕获日志
     * @param context Job执行上下文
     * @return 输出流
     */
    public static ByteArrayOutputStream startLogCapture(JobExecutionContext context) {
        JobDataMap dataMap = context.getJobDetail().getJobDataMap();
        String logOutputStreamId = dataMap.getString("logOutputStreamId");
        
        if (logOutputStreamId != null) {
            try {
                // 获取日志输出流
                ByteArrayOutputStream logOutputStream = QuartzJobServiceImpl.getJobLogOutputStream(logOutputStreamId);
                if (logOutputStream != null) {
                    // 创建一个新的输出流，将输出复制到logOutputStream和原始System.out
                    TeeOutputStream teeOut = new TeeOutputStream(ORIGINAL_STDOUT, logOutputStream);
                    TeeOutputStream teeErr = new TeeOutputStream(ORIGINAL_STDERR, logOutputStream);
                    
                    // 创建新的PrintStream来捕获输出
                    PrintStream captureOut = new PrintStream(teeOut, true);
                    PrintStream captureErr = new PrintStream(teeErr, true);
                    
                    // 重定向系统输出流
                    System.setOut(captureOut);
                    System.setErr(captureErr);
                    
                    // 添加明显的任务开始标记
                    System.out.println("=================== 任务开始执行 ===================");
                    
                    // 初始化SLF4J日志捕获
                    MemoryAppender.initializeForTask(logOutputStreamId, logOutputStream);
                    
                    return logOutputStream;
                }
            } catch (Exception e) {
                log.error("开始捕获日志失败", e);
            }
        }
        return null;
    }
    
    /**
     * 结束捕获日志，恢复原始输出流
     */
    public static void endLogCapture(JobExecutionContext context) {
        try {
            JobDataMap dataMap = context.getJobDetail().getJobDataMap();
            String logOutputStreamId = dataMap.getString("logOutputStreamId");
            
            // 添加明显的任务结束标记
            System.out.println("=================== 任务执行完成 ===================");
            
            // 恢复原始输出流
            System.setOut(ORIGINAL_STDOUT);
            System.setErr(ORIGINAL_STDERR);
            
            // 清理SLF4J日志捕获
            if (logOutputStreamId != null) {
                MemoryAppender.cleanupForTask(logOutputStreamId);
            }
        } catch (Exception e) {
            log.error("结束日志捕获失败", e);
        }
    }
    
    /**
     * 用于将输出同时发送到多个输出流的辅助类
     */
    private static class TeeOutputStream extends OutputStream {
        private final OutputStream[] streams;
        
        public TeeOutputStream(OutputStream... streams) {
            this.streams = streams;
        }
        
        @Override
        public void write(int b) throws IOException {
            for (OutputStream stream : streams) {
                stream.write(b);
            }
        }
        
        @Override
        public void write(byte[] b) throws IOException {
            for (OutputStream stream : streams) {
                stream.write(b);
            }
        }
        
        @Override
        public void write(byte[] b, int off, int len) throws IOException {
            for (OutputStream stream : streams) {
                stream.write(b, off, len);
            }
        }
        
        @Override
        public void flush() throws IOException {
            for (OutputStream stream : streams) {
                stream.flush();
            }
        }
        
        @Override
        public void close() throws IOException {
            for (OutputStream stream : streams) {
                stream.close();
            }
        }
    }
} 