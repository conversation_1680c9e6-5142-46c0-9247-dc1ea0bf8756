package org.jeecg.modules.system.rule;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.common.handler.IFillRuleHandler;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.common.util.YouBianCodeUtil;
import org.jeecg.modules.system.util.LocalDateTimeUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.util.List;

/**
 * 通用单据流水号生成规则
 */
public class CommonOrderRule implements IFillRuleHandler {

    @Override
    @SuppressWarnings("unchecked")
    public Object execute(JSONObject params, JSONObject formData) {
        // 从参数中获取可变要素
        String serviceBeanName = params.getString("serviceBeanName");
        String entityClassName = params.getString("entityClassName");
        String prefix = params.getString("prefix");
        String codeFieldName = params.getString("codeFieldName");
        String datePattern = params.getString("datePattern");
        Integer digit = params.getInteger("digit");

        // 设置默认值
        if (!StringUtils.hasText(serviceBeanName)) {
            throw new RuntimeException("serviceBeanName参数不能为空，请指定对应Service的Bean名称");
        }

        if (!StringUtils.hasText(entityClassName)) {
            throw new RuntimeException("entityClassName参数不能为空，请指定对应实体类名称");
        } else {
            // 如果entityClassName中不包含'.'则说明只是简单类名，需要加上默认前缀
            if (!entityClassName.contains(".")) {
                entityClassName = "org.jeecg.modules.admin.entity." + entityClassName;
            }
        }

        if (!StringUtils.hasText(prefix)) {
            prefix = "DH";
        }
        if (!StringUtils.hasText(codeFieldName)) {
            codeFieldName = "bill_no";
        }
        if (!StringUtils.hasText(datePattern)) {
            datePattern = LocalDateTimeUtils.YYYYMMDD;
        }
        if (digit == null) {
            digit = 4;
        }

        // 获取Service实例
        Object service = SpringContextUtils.getBean(serviceBeanName);
        if (service == null) {
            throw new RuntimeException("无法获取指定的Service实例：" + serviceBeanName);
        }

        // 构建日期规则
        String timeRule = LocalDateTimeUtils.formatNow(datePattern);

        // 初始化基础编码（用于当日第一条记录时的初始值）
        String initialIsbn = prefix + timeRule + String.format("%0" + digit + "d", 0);

        // 使用QueryWrapper构建查询，获取当日最新的一条编码
        QueryWrapper<?> q = new QueryWrapper<>();
        // 指定只查询codeFieldName这一列
        q.select(codeFieldName)
                .likeRight(codeFieldName, prefix + timeRule)
                .orderByDesc(codeFieldName)
                .last("LIMIT 1");

        String isbn = null;
        try {
            Method listObjsMethod = service.getClass().getMethod("listObjs", com.baomidou.mybatisplus.core.conditions.Wrapper.class);
            List<Object> list = (List<Object>) listObjsMethod.invoke(service, q);

            if (list != null && !list.isEmpty()) {
                isbn = (String) list.get(0);
            }
        } catch (Exception e) {
            throw new RuntimeException("构建查询条件或执行查询失败", e);
        }

        // 如果当日还没有生成过编码，则使用初始值
        if (isbn == null) {
            isbn = initialIsbn;
        }

        // 调用我们自定义的简单自增方法获取下一个编码
        return YouBianCodeUtil.getNextSimpleCode(isbn, prefix, timeRule, digit);
    }

}
