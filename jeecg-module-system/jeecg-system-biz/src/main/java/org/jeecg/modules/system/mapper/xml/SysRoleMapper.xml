<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.system.mapper.SysRoleMapper">

    <select id="listAllSysRole" resultType="org.jeecg.modules.system.entity.SysRole">
        SELECT * from sys_role
        WHERE 1=1
        <if test="role.roleName!='' and role.roleName!=null">
            <bind name="bindKeyword" value="'%'+role.roleName+'%'"/>
            AND role_name like #{bindKeyword}
        </if>
        <if test="role.roleCode!='' and role.roleCode!=null">
            <bind name="bindRoleCode" value="'%'+role.roleCode+'%'"/>
            AND role_code like #{bindRoleCode}
        </if>
    </select>
   

    <select id="getRoleNoTenant" resultType="org.jeecg.modules.system.entity.SysRole">
        SELECT * from sys_role
        WHERE role_code = #{roleCode}
    </select>
    
</mapper>