package org.jeecg.modules.quartz.job;

import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 基础定时任务类
 * 提供日志捕获功能，所有需要日志实时反馈的定时任务都应该继承此类
 */
@Slf4j
public abstract class BaseQuartzJob implements Job {

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        ByteArrayOutputStream logStream = null;
        
        // 记录任务开始时间
        String startTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        
        try {
            // 开始捕获日志
            logStream = LogCaptureEnhancer.startLogCapture(context);
            
            // 输出任务开始信息
            System.out.println("任务开始执行时间: " + startTime);
            System.out.println("任务名称: " + context.getJobDetail().getKey().getName());
            System.out.println("任务参数: " + context.getJobDetail().getJobDataMap().getString("parameter"));
            
            // 执行实际任务
            executeTask(context);
            
            // 记录任务结束时间
            String endTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            System.out.println("任务执行结束时间: " + endTime);
            System.out.println("任务执行状态: 成功");
            
        } catch (Exception e) {
            // 记录任务结束时间
            String endTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            System.out.println("任务执行结束时间: " + endTime);
            System.out.println("任务执行状态: 失败");
            
            // 直接在控制台打印异常，这样会被我们的日志捕获器捕获
            System.err.println("任务执行异常: ");
            e.printStackTrace(new PrintStream(System.err));
            
            log.error("执行定时任务失败", e);
            throw new JobExecutionException(e);
        } finally {
            // 结束日志捕获
            LogCaptureEnhancer.endLogCapture(context);
        }
    }
    
    /**
     * 执行具体任务逻辑
     * 子类需要实现此方法
     * @param context 任务执行上下文
     * @throws Exception 任务执行异常
     */
    protected abstract void executeTask(JobExecutionContext context) throws Exception;
} 