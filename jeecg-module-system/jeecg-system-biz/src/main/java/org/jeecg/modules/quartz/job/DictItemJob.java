package org.jeecg.modules.quartz.job;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.system.service.ISysDictItemService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 示例不带参定时任务
 * 
 * <AUTHOR>
 */
@Slf4j
public class DictItemJob implements Job {
	@Autowired
	private ISysDictItemService sysDictItemService;

	@Override
	public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
		sysDictItemService.syncItemUnitData();
		sysDictItemService.syncMaterielTypeData();
    }
}
