package org.jeecg.modules.quartz.job;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.admin.service.IMesInterfaceService;
import org.jeecg.modules.admin.service.IWmsSpecMatchItemService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;

/**
 * 示例不带参定时任务
 * 
 * <AUTHOR>
 */
@Slf4j
public class MaterialJob implements Job {
	@Autowired
	private IWmsSpecMatchItemService mesInterfaceService;

	@Override
	public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        mesInterfaceService.syncData();
    }
}
