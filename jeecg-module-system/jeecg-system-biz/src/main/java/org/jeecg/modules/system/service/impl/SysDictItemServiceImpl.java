package org.jeecg.modules.system.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jeecg.common.api.dto.LogDTO;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.admin.entity.MesInterface;
import org.jeecg.modules.admin.service.IMesInterfaceService;
import org.jeecg.modules.base.service.BaseCommonService;
import org.jeecg.modules.system.entity.SysDict;
import org.jeecg.modules.system.entity.SysDictItem;
import org.jeecg.modules.system.mapper.SysDictItemMapper;
import org.jeecg.modules.system.service.ISysDictItemService;
import org.jeecg.modules.system.service.ISysDictService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 字典项同步服务示例
 */
@Service
public class SysDictItemServiceImpl extends ServiceImpl<SysDictItemMapper, SysDictItem> implements ISysDictItemService {
    private static final Logger log = LoggerFactory.getLogger(SysDictItemServiceImpl.class);

    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private BaseCommonService baseCommonService;
    @Autowired
    private IMesInterfaceService mesInterfaceService;
    @Autowired
    private ISysDictService sysDictService;

    // 分页大小，根据接口要求
    private static final int PAGE_SIZE = 10;

    @Override
    public List<SysDictItem> selectItemsByMainId(String mainId) {
        return this.baseMapper.selectItemsByMainId(mainId);
    }

    /**
     * 同步物料单位字典
     * 字典编码: item_unit
     * 字典名称: 物料单位
     * 数据项: fname作为itemText, funitid作为itemValue
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncItemUnitData() {
        String accessToken = redisTemplate.opsForValue().get("mes_login_access_token");
        if (accessToken == null) {
            throw new RuntimeException("未获取到MES登录token！");
        }

        MesInterface unitInterface = getMesInterfaceByCode("JK015");
        List<JSONObject> allData = fetchAllData(unitInterface, accessToken);
        // 将数据映射为字典项信息
        List<SysDictItem> dictItems = new ArrayList<>(allData.size());
        Date now = new Date();
        for (JSONObject obj : allData) {
            String fname = obj.getString("fname");
            Long funitid = obj.getLong("funitid");
            if (fname == null || funitid == null) continue;

            SysDictItem item = new SysDictItem();
            item.setItemText(fname);
            item.setItemValue(String.valueOf(funitid));
            item.setStatus(1);
            item.setCreateTime(now);
            dictItems.add(item);
        }

        syncDictData("item_unit", "物料单位", dictItems);
    }

    /**
     * 同步物料类型字典
     * 字典编码: materiel_type
     * 字典名称: 物料类型
     * 数据项: fname作为itemText, fcategoryid作为itemValue
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncMaterielTypeData() {
        String accessToken = redisTemplate.opsForValue().get("mes_login_access_token");
        if (accessToken == null) {
            throw new RuntimeException("未获取到MES登录token！");
        }

        // 假设接口编码为 "JK016" 对应物料类型请求(请根据实际配置修改)
        MesInterface typeInterface = getMesInterfaceByCode("JK016");
        List<JSONObject> allData = fetchAllData(typeInterface, accessToken);

        List<SysDictItem> dictItems = new ArrayList<>(allData.size());
        Date now = new Date();
        for (JSONObject obj : allData) {
            String fname = obj.getString("fname");
            Long fcategoryid = obj.getLong("fcategoryid");
            if (fname == null || fcategoryid == null) continue;

            SysDictItem item = new SysDictItem();
            item.setItemText(fname);
            item.setItemValue(String.valueOf(fcategoryid));
            item.setStatus(1);
            item.setCreateTime(now);
            dictItems.add(item);
        }

        syncDictData("materiel_type", "物料类型", dictItems);
    }

    /**
     * 通用的字典同步方法
     * 如果已存在该字典编码，则删除原有项并新增新的字典项
     * 如果不存在，则新增字典主表记录并新增字典项
     */
    private void syncDictData(String dictCode, String dictName, List<SysDictItem> dictItems) {
        SysDict sysDict = sysDictService.getOne(new QueryWrapper<SysDict>().eq("dict_code", dictCode));
        boolean isNew = false;
        if (sysDict == null) {
            sysDict = new SysDict();
            sysDict.setDictCode(dictCode);
            sysDict.setDictName(dictName);
            sysDict.setDelFlag(0);
            sysDict.setCreateTime(new Date());
            sysDictService.save(sysDict);
            isNew = true;
        } else {
            // 删除该字典下所有字典项
            deleteDictItemsByDictId(sysDict.getId());
        }

        // 插入新的字典项
        for (SysDictItem item : dictItems) {
            item.setDictId(sysDict.getId());
        }
        if (!dictItems.isEmpty()) {
            this.saveBatch(dictItems);
        }

        log.info("字典[{}]同步完成，新增{}条项记录，dictId={}", dictCode, dictItems.size(), sysDict.getId());
    }

    /**
     * 根据接口信息和token全量获取数据
     */
    private List<JSONObject> fetchAllData(MesInterface mi, String token) {
        int total = getTotalCount(mi, token);
        if (total == 0) {
            return Collections.emptyList();
        }
        int totalPages = (int) Math.ceil((double) total / PAGE_SIZE);
        List<JSONObject> allData = new ArrayList<>(total);
        for (int pageNum = 1; pageNum <= totalPages; pageNum++) {
            List<JSONObject> pageData = fetchPageData(mi, token, pageNum);
            allData.addAll(pageData);
        }
        return allData;
    }

    /**
     * 获取total数量
     */
    private int getTotalCount(MesInterface mi, String token) {
        String url = mi.getInterfaceUrl() + "?pageNum=1&pageSize=1";
        JSONObject json = doGet(url, token);
        return json.getInteger("total");
    }

    /**
     * 获取指定页的数据
     */
    private List<JSONObject> fetchPageData(MesInterface mi, String token, int pageNum) {
        String url = mi.getInterfaceUrl() + "?pageNum=" + pageNum + "&pageSize=" + PAGE_SIZE;
        JSONObject json = doGet(url, token);
        JSONArray rows = json.getJSONArray("rows");
        if (rows == null || rows.isEmpty()) {
            return Collections.emptyList();
        }
        List<JSONObject> list = new ArrayList<>(rows.size());
        for (int i = 0; i < rows.size(); i++) {
            list.add(rows.getJSONObject(i));
        }
        return list;
    }

    private MesInterface getMesInterfaceByCode(String code) {
        MesInterface mesInterface = mesInterfaceService.getOne(new QueryWrapper<MesInterface>().eq("interface_code", code));
        if (mesInterface == null) {
            throw new RuntimeException("未找到接口编码为 " + code + " 的MES接口配置信息！");
        }
        if (!"1".equals(mesInterface.getInterfaceStatus())) {
            throw new RuntimeException("接口 " + code + " 未启用！");
        }
        if (!"GET".equalsIgnoreCase(mesInterface.getRequestMethod())) {
            throw new RuntimeException("接口 " + code + " 的请求方式不是GET！");
        }
        return mesInterface;
    }

    private JSONObject doGet(String url, String token) {
        long startTime = System.currentTimeMillis();
        LoginUser sysUser = new LoginUser();
        sysUser.setUsername("system");
        sysUser.setRealname("系统用户");

        String requestParam = "";
        if (url.contains("?")) {
            requestParam = url.substring(url.indexOf("?") + 1);
        }

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet get = new HttpGet(url);
            get.setHeader("Authorization", "Bearer " + token);

            try (CloseableHttpResponse response = httpClient.execute(get)) {
                long endTime = System.currentTimeMillis();
                long costTime = endTime - startTime;
                int statusCode = response.getStatusLine().getStatusCode();
                String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                if (statusCode == 200) {
                    return JSONObject.parseObject(responseBody);
                } else {
                    log.error("请求失败，状态码：{}，URL：{}，参数：{}，响应：{}", statusCode, url, requestParam, responseBody);
                    baseCommonService.addLog(new LogDTO(
                            "调用MES接口失败",
                            CommonConstant.LOG_TYPE_3,
                            null,
                            sysUser,
                            requestParam,
                            "GET",
                            url,
                            "syncDictData",
                            "状态码：" + statusCode + "，响应：" + responseBody,
                            getLocalIpAddress(),
                            costTime
                    ));
                    throw new RuntimeException("调用MES接口失败，状态码：" + statusCode + "，响应：" + responseBody);
                }
            }
        } catch (IOException e) {
            long endTime = System.currentTimeMillis();
            long costTime = endTime - startTime;
            log.error("请求IO异常，URL：{}，异常信息：{}", url, e.getMessage());
            baseCommonService.addLog(new LogDTO(
                    "调用MES接口IO异常",
                    CommonConstant.LOG_TYPE_3,
                    null,
                    sysUser,
                    requestParam,
                    "GET",
                    url,
                    "syncDictData",
                    "IO异常：" + e.getMessage(),
                    getLocalIpAddress(),
                    costTime
            ));
            throw new RuntimeException("调用MES接口时发生IO异常", e);
        }
    }

    /**
     * 删除指定字典ID下所有字典项
     */
    private void deleteDictItemsByDictId(String dictId) {
        QueryWrapper<SysDictItem> query = new QueryWrapper<>();
        query.eq("dict_id", dictId);
        this.remove(query);
    }

    private String getLocalIpAddress() {
        try {
            InetAddress localHost = InetAddress.getLocalHost();
            return localHost.getHostAddress();
        } catch (UnknownHostException e) {
            return "未知";
        }
    }
}
