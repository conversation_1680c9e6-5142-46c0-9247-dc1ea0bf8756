<#include "/common/utils.ftl">
<template>
  <div>
    <BasicForm @register="registerForm" ref="formRef"/>
  <!-- 子表单区域 -->
    <a-tabs v-model:activeKey="activeKey" animated @change="handleChangeTabs">
<#list subTables as sub><#rt/>
  <#assign refKey = sub.entityName?uncap_first/>
  <#if sub.foreignRelationType =='1'>
      <a-tab-pane tab="${sub.ftlDescription}" key="${refKey}" :forceRender="true">
        <${sub.entityName}Form ref="${sub.entityName?uncap_first}Form" :disabled="formDisabled"></${sub.entityName}Form>
      </a-tab-pane>

  <#else>
      <a-tab-pane tab="${sub.ftlDescription}" key="${refKey}" :forceRender="true">
        <JVxeTable
          v-if="${sub.entityName?uncap_first}Table.show"      
          keep-source
          resizable
          ref="${refKey}"
          :loading="${sub.entityName?uncap_first}Table.loading"
          :columns="${sub.entityName?uncap_first}Table.columns"
          :dataSource="${sub.entityName?uncap_first}Table.dataSource"
          :height="340"
          :disabled="formDisabled"
          :rowNumber="true"
          :rowSelection="true"
          :toolbar="true"
          />
      </a-tab-pane>
  </#if>
</#list>
    </a-tabs>
    <div style="width: 100%;text-align: center;margin-top: 10px;" v-if="showFlowSubmitButton">
      <a-button preIcon="ant-design:check-outlined" style="width: 126px" type="primary" @click="handleSubmit">提 交</a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
    import { defHttp } from '/@/utils/http/axios';
    import {ref, computed, unref,reactive, onMounted, defineProps } from 'vue';
    import {BasicForm, useForm} from '/@/components/Form/index';
    import { JVxeTable } from '/@/components/jeecg/JVxeTable'
    import { useJvxeMethod } from '/@/hooks/system/useJvxeMethods.ts'
    <#list subTables as sub>
      <#if sub.foreignRelationType =='1'>
    import ${sub.entityName}Form from './${sub.entityName}Form.vue'
      </#if>
    </#list>
    import {formSchema<#list subTables as sub><#if sub.foreignRelationType =='0'>,${sub.entityName?uncap_first}JVxeColumns</#if></#list>} from '../${entityName}.data';
    import {saveOrUpdate<#list subTables as sub>,query${sub.entityName}</#list>} from '../${entityName}.api';
    import { VALIDATE_FAILED } from '/@/utils/common/vxeUtils'
    const isUpdate = ref(true);
    
    const refKeys = ref([<#list subTables as sub>'${sub.entityName?uncap_first}', </#list>]);
    <#assign hasOne2Many = false>
    <#assign hasOne2One = false>
    const activeKey = ref('${subTables[0].entityName?uncap_first}');
<#list subTables as sub>
<#if sub.foreignRelationType =='0'>
 <#assign hasOne2Many = true>
    const ${sub.entityName?uncap_first} = ref();
</#if>
<#if sub.foreignRelationType =='1'>
 <#assign hasOne2One = true>
    const ${sub.entityName?uncap_first}Form = ref();
</#if>
</#list>
    const tableRefs = {<#list subTables as sub><#if sub.foreignRelationType =='0'>${sub.entityName?uncap_first}, <#assign hasOne2Many = true></#if></#list>};
   <#list subTables as sub>
   <#if sub.foreignRelationType =='0'>
    const ${sub.entityName?uncap_first}Table = reactive({
          loading: false,
          dataSource: [],
          columns:${sub.entityName?uncap_first}JVxeColumns,
          show: false
    })
    </#if>
   </#list>

    const props = defineProps({
      formData: { type: Object, default: ()=>{} },
      formBpm: { type: Boolean, default: true }
    });
    const formDisabled = computed(()=>{
      if(props.formBpm === true){
        if(props.formData.disabled === false){
          return false;
        }
      }
      return true;
    });
    // 是否显示提交按钮
    const showFlowSubmitButton = computed(()=>{
      if(props.formBpm === true){
        if(props.formData.disabled === false){
          return true
        }
      }
      return false
    });
    
    //表单配置
    const [registerForm, {setProps,resetFields, setFieldsValue, validate}] = useForm({
        labelWidth: 150,
        schemas: formSchema,
        showActionButtonGroup: false,
        baseColProps: {span: ${getFormSpan(tableVo.fieldRowNum?default(1))}}
    });

    onMounted(()=>{
      initFormData();
    });
    //渲染流程表单数据
    const queryByIdUrl = '/${entityPackage}/${entityName?uncap_first}/queryById';
    async function initFormData(){
      if(props.formBpm === true){
        await reset();
        let params = {id: props.formData.dataId};
        const data = await defHttp.get({url: queryByIdUrl, params});
        //表单赋值
        await setFieldsValue({
          ...data
        });
        <#list subTables as sub><#rt/>
        <#if sub.foreignRelationType =='1'>
        ${sub.entityName?uncap_first}Form.value.initFormData(query${sub.entityName}, data.id)
        </#if>
        </#list>
        <#list subTables as sub><#rt/>
        <#if sub.foreignRelationType =='0'>
        requestSubTableData(query${sub.entityName}, {id: data.id}, ${sub.entityName?uncap_first}Table, ()=>{
          ${sub.entityName?uncap_first}Table.show = true;
        });
        </#if>
        </#list>
        // 隐藏底部时禁用整个表单
        setProps({ disabled: formDisabled.value })
      }
    }
    
    //方法配置
    const [handleChangeTabs,handleSubmit,requestSubTableData,formRef] = useJvxeMethod(requestAddOrEdit,classifyIntoFormData,tableRefs,activeKey,refKeys<#if hasOne2One==true>,validateSubForm</#if>);

    async function reset(){
      await resetFields();
      activeKey.value = '${subTables[0].entityName?uncap_first}';
      <#list subTables as sub>
        <#if sub.foreignRelationType =='0'>
      ${sub.entityName?uncap_first}Table.dataSource = [];
        </#if>
        <#if sub.foreignRelationType =='1'>
      ${sub.entityName?uncap_first}Form.value.resetFields();
        </#if>
      </#list>
    }
    function classifyIntoFormData(allValues) {
         let main = Object.assign({}, allValues.formValue)
         return {
           ...main, // 展开
 <#assign subManyIndex = 0>
 <#list subTables as sub><#rt/>
 <#if sub.foreignRelationType =='0'>
           ${sub.entityName?uncap_first}List: allValues.tablesValue[${subManyIndex}].tableData,
           <#assign subManyIndex = subManyIndex+1>
 <#else>
           ${sub.entityName?uncap_first}List: ${sub.entityName?uncap_first}Form.value.getFormData(),
 </#if>
 </#list>
         }
       }
      <#if hasOne2One==true>
     //校验所有一对一子表表单
     function validateSubForm(allValues){
         return new Promise((resolve,reject)=>{
             Promise.all([
             <#list subTables as sub><#rt/>
            <#if sub.foreignRelationType =='1'>
                  ${sub.entityName?uncap_first}Form.value.validateForm(${sub_index}),
            </#if>
          </#list>
             ]).then(() => {
                 resolve(allValues)
             }).catch(e => {
                 if (e.error === VALIDATE_FAILED) {
                     // 如果有未通过表单验证的子表，就自动跳转到它所在的tab
                     activeKey.value = e.index == null ? unref(activeKey) : refKeys.value[e.index]
                 } else {
                     console.error(e)
                 }
             })
         })
     }
    </#if>
    //表单提交事件
    async function requestAddOrEdit(values) {
      //提交表单
      await saveOrUpdate(values, true);
    }
</script>

<style lang="less" scoped>
	/** 时间和数字输入框样式 */
    :deep(.ant-input-number){
		width: 100%
	}

	:deep(.ant-calendar-picker){
		width: 100%
	}
</style>