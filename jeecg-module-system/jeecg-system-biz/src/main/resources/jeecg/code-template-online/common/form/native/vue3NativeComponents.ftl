<#if need_select_tag>
      JDictSelectTag,
</#if>
<#if need_switch>
      JSwitch,
</#if>
<#if need_multi>
      JSelectMultiple,
</#if>
<#if need_search>
      JSearchSelect,
</#if>
<#if need_popup>
      JPopup,
</#if>
<#if need_category>
      JCategorySelect,
</#if>
<#if need_dept>
      JSelectDept,
</#if>
<#if need_dept_user>
<#-- update-begin---author:chenrui ---date:20240102  for：[issue/#5711]修复用户选择组件在生成代码后变成部门用户选择组件---------- -->
      JSelectUser,
<#-- update-end---author:chenrui ---date:20240102  for：[issue/#5711]修复用户选择组件在生成代码后变成部门用户选择组件---------- -->
</#if>
<#if need_select_tree>
      J<PERSON>reeSelect,
</#if>
<#if need_time>
      TimePicker,
</#if>
<#if need_pca>
      J<PERSON><PERSON><PERSON>inkage,
</#if>
<#if need_upload>
      JUpload,
</#if>
<#if need_image_upload>
      JImageUpload,
</#if>
<#if need_markdown>
      JMarkdownEditor,
</#if>
<#if need_editor>
      JEditor,
</#if>
<#if need_checkbox>
      JCheckbox,
</#if>