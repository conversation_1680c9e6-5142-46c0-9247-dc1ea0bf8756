alter table wms_inventory_count_plan add from_materiel_code varchar(32)  DEFAULT NULL COMMENT 'from物料号';
alter table wms_inventory_count_plan add to_materiel_code varchar(32)  DEFAULT NULL COMMENT 'to物料号';
alter table wms_inventory_count_plan add from_batch_code varchar(32)  DEFAULT NULL COMMENT 'from批次号';
alter table wms_inventory_count_plan add to_batch_code varchar(32)  DEFAULT NULL COMMENT 'to批次号';
alter table wms_inventory_count_plan add from_lpn varchar(32)  DEFAULT NULL COMMENT 'from托盘号';
alter table wms_inventory_count_plan add to_lpn varchar(32)  DEFAULT NULL COMMENT 'to托盘号';
alter table wms_inventory_count_plan add locate_code varchar(32)  DEFAULT NULL COMMENT '货位';
alter table wms_inventory_count_plan add warehouse_code varchar(32)  DEFAULT NULL COMMENT '库房';
alter table wms_inventory_count_plan add lk_locate varchar(10)  DEFAULT NULL COMMENT '是否立库盘点';


alter table wms_inventory_count_task add locate_code varchar(32)  DEFAULT NULL COMMENT '货位号';
alter table wms_inventory_count_task add lpn varchar(32)  DEFAULT NULL COMMENT '托盘号';
alter table wms_inventory_count_task add task_code varchar(32)  DEFAULT NULL COMMENT '盘点任务号';
alter table wms_inventory_count_task add batch_code varchar(32)  DEFAULT NULL COMMENT '批号';
alter table wms_inventory_count_task add target_locate_code varchar(32)  DEFAULT NULL COMMENT '目标货位号';


alter table wms_serial_num add data_version int NOT NULL DEFAULT '1' COMMENT '版本号';
INSERT INTO `wms_serial_num` (`serial_type`, `date_format`, `sys_date`, `prefix`, `seek_val`, `seek_max`, `seek_min`, `seek_len`, `reset_cyc`, `comments`, `data_version`) VALUES ('PDCODE', 'YYMMDD', '20250116', 'PD', 2, 9999, 1, 4, 1, '盘点流水号', 8);
INSERT INTO `wms_serial_num` (`serial_type`, `date_format`, `sys_date`, `prefix`, `seek_val`, `seek_max`, `seek_min`, `seek_len`, `reset_cyc`, `comments`, `data_version`) VALUES ('CHECKTASKCODE', 'YYMMDD', '20250116', 'RC', 3, 999, 1, 4, 1, '盘点任务流水号', 11);


alter table wms_inventory_count_plan add wcs_status_sync varchar(10) DEFAULT '0' COMMENT '是否下发WCS 0否 1是';
alter table wms_inventory_count_plan add erp_sync varchar(10) DEFAULT '0' COMMENT 'ERP同步状态 0初始 1审核中 2同步成功 3同步失败';
INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `tenant_id`, `low_app_id`) VALUES ('1899275915165220866', '盘点计划下发是否成功', 'wcs_status_sync', '0否 1是', 0, 'admin', '2025-03-11 09:47:20', NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `item_color`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1899275986204147714', '1899275915165220866', '否', '0', NULL, NULL, 1, 1, 'admin', '2025-03-11 09:47:37', NULL, NULL);
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `item_color`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1899276029963321346', '1899275915165220866', '1', '是', NULL, NULL, 2, 1, 'admin', '2025-03-11 09:47:47', NULL, NULL);


alter table wms_inventory_count_task add confirm_quantity double(10,3) DEFAULT NULL COMMENT '确认数量';