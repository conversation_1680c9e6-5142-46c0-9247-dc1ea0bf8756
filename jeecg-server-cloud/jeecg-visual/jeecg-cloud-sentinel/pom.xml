<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>jeecg-visual</artifactId>
        <groupId>org.jeecgframework.boot</groupId>
        <version>3.6.3</version>
    </parent>
    <artifactId>jeecg-cloud-sentinel</artifactId>
    <name>jeecg-cloud-sentinel</name>
    <description>sentinel启动模块</description>

    <dependencies>
        <dependency>
            <groupId>org.jeecgframework.cloud</groupId>
            <artifactId>sentinel-dashboard</artifactId>
            <version>1.8.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>sentinel-web-servlet</artifactId>
                    <groupId>com.alibaba.csp</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>sentinel-transport-simple-http</artifactId>
                    <groupId>com.alibaba.csp</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>sentinel-parameter-flow-control</artifactId>
                    <groupId>com.alibaba.csp</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>sentinel-core</artifactId>
                    <groupId>com.alibaba.csp</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>sentinel-api-gateway-adapter-common</artifactId>
                    <groupId>com.alibaba.csp</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-datasource-nacos</artifactId>
            <version>1.8.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>sentinel-core</artifactId>
                    <groupId>com.alibaba.csp</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-core</artifactId>
            <version>1.8.3</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-web-servlet</artifactId>
            <version>1.8.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>sentinel-core</artifactId>
                    <groupId>com.alibaba.csp</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-transport-simple-http</artifactId>
            <version>1.8.3</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-parameter-flow-control</artifactId>
            <version>1.8.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>sentinel-core</artifactId>
                    <groupId>com.alibaba.csp</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-api-gateway-adapter-common</artifactId>
            <version>1.8.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>sentinel-parameter-flow-control</artifactId>
                    <groupId>com.alibaba.csp</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>sentinel-core</artifactId>
                    <groupId>com.alibaba.csp</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.14</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4.5</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpasyncclient</artifactId>
            <version>4.1.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore-nio</artifactId>
            <version>4.4.6</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
