server:
  port: 5002
spring:
  application:
    name: seata-account
  main:
    allow-bean-definition-overriding: true
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    dynamic:
      seata: true    # 开启对 seata的支持
      primary: account
      datasource:
        account:
          url: *****************************************************************************************************************************************************************************
          username: root
          password: root
          driver-class-name: com.mysql.cj.jdbc.Driver
          schema: classpath:sql/schema-account.sql
seata:
  enable-auto-data-source-proxy: false
  service:
    grouplist:
      default: 127.0.0.1:8091
    vgroup-mapping:
      springboot-seata-group: default
  # seata 事务组编号 用于TC集群名
  tx-service-group: springboot-seata-group