<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jeecg-cloud-test</artifactId>
        <groupId>org.jeecgframework.boot</groupId>
        <version>3.6.3</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <description>公共测试模块</description>
    <artifactId>jeecg-cloud-test-more</artifactId>

    <dependencies>
        <!-- 引入jeecg-boot-starter-cloud依赖 -->
        <dependency>
            <groupId>org.jeecgframework.boot</groupId>
            <artifactId>jeecg-boot-starter-cloud</artifactId>
            <!--system模块需要排除jeecg-system-cloud-api-->
            <exclusions>
                <exclusion>
                    <groupId>org.jeecgframework.boot</groupId>
                    <artifactId>jeecg-system-cloud-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--定时任务-->
        <dependency>
            <groupId>org.jeecgframework.boot</groupId>
            <artifactId>jeecg-boot-starter-job</artifactId>
        </dependency>
        <!--rabbitmq消息队列-->
        <dependency>
            <groupId>org.jeecgframework.boot</groupId>
            <artifactId>jeecg-boot-starter-rabbitmq</artifactId>
        </dependency>
        <!-- 分布式锁依赖 -->
        <dependency>
            <groupId>org.jeecgframework.boot</groupId>
            <artifactId>jeecg-boot-starter-lock</artifactId>
        </dependency>
    </dependencies>

</project>