version: '2'
services:
  jeecg-boot-mysql:
    build:
      context: ../db
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_ROOT_HOST: '%'
      TZ: Asia/Shanghai
    restart: always
    container_name: jeecg-boot-mysql
    command:
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --explicit_defaults_for_timestamp=true
      --lower_case_table_names=1
      --max_allowed_packet=128M
      --default-authentication-plugin=caching_sha2_password
    ports:
      - 3306:3306
    networks:
      - jeecg-boot

  jeecg-boot-redis:
    image: redis:5.0
    ports:
      - 6379:6379
    restart: always
    container_name: jeecg-boot-redis
    hostname: jeecg-boot-redis
    networks:
      - jeecg-boot


networks:
  jeecg-boot:
    name: jeecg_boot

#  jeecg-boot-rabbitmq:
#    image: rabbitmq:3.7.7-management
#    ports:
#      - 5672:5672
#      - 15672:15672
#    restart: always
#    container_name: jeecg-boot-rabbitmq
#    hostname: jeecg-boot-rabbitmq
#    environment:
#      RABBITMQ_DEFAULT_USER: guest
#      RABBITMQ_DEFAULT_PASS: guest
